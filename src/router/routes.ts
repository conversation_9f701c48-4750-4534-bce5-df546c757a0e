import type { RouteRecordRaw } from 'vue-router'

/**
 * 路由配置
 * @type {RouteRecordRaw[]}
 */
const routes: RouteRecordRaw[] = [
  {
    path: '/',
    component: () => import('../components/layouts/WorkspaceLayout/index.vue'),
    children: [
      {
        path: '',
        name: 'Dashboard',
        component: () => import('../views/home/<USER>'),
        meta: {
          title: '首页',
        },
      },
      {
        path: 'apps',
        name: 'Apps',
        component: () => import('../views/apps/index.vue'),
        meta: {
          title: '应用中心',
        },
      },
      {
        path: 'notices',
        name: 'Notices',
        component: () => import('../views/notices/index.vue'),
        meta: {
          title: '通知公告',
        },
      },
      {
        path: 'notices/:id',
        name: 'NoticeDetail',
        component: () => import('../views/notices/detail.vue'),
        meta: {
          title: '通知详情',
        },
      },
      {
        path: 'settings',
        name: 'Settings',
        component: () => import('../views/settings/index.vue'),
        meta: {
          title: '个人设置',
        },
      },
      {
        path: '/:pathMatch(.*)*',
        name: 'NotFound',
        component: () => import('../views/error/404.vue'),
        meta: {
          title: '页面未找到',
          requiresAuth: true,
        },
      },
    ],
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('../views/auth/login.vue'),
    meta: {
      title: '登录',
      requiresAuth: false,
    },
  },
  {
    path: '/social-login/:source',
    name: 'Callback',
    component: () => import('../views/auth/callback.vue'),
    meta: {
      title: '认证回调',
      requiresAuth: false,
    },
  },
  {
    path: '/dingtalk',
    name: 'Dingtalk',
    component: () => import('../views/auth/dingtalk.vue'),
    meta: {
      title: '钉钉登录',
      requiresAuth: false,
    },
  },
  {
    path: '/demo',
    name: 'Demo',
    component: () => import('../views/_demo/index.vue'),
    meta: {
      title: '演示页面',
    },
  },
  {
    path: '/admin',
    component: () => import('../components/layouts/DashboardLayout/index.vue'),
    children: [
      {
        path: '',
        name: 'Admin',
        component: () => import('../views/_admin/index.vue'),
        meta: {
          title: '管理后台',
        },
      },
      {
        path: '/nav/1/item-1',
        name: 'Nav1',
        component: () => import('../views/_admin/nav/1/item-1.vue'),
        meta: {
          title: '导航 1',
        },
      },
      {
        path: '/nav/2',
        name: 'Nav2',
        component: () => import('../views/_admin/nav/2.vue'),
        meta: {
          title: '导航 2',
        },
      },
      {
        path: '/nav/4',
        name: 'Nav4',
        component: () => import('../views/_admin/nav/4.vue'),
        meta: {
          title: '导航 4',
        },
      },
    ],
  },
]

export default routes
