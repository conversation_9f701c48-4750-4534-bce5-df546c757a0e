import { createRouter, createWebHistory } from 'vue-router'
import routes from './routes'

const router = createRouter({
  history: createWebHistory(import.meta.env.VITE_BASE_URL),
  routes,
})

export default router

router.beforeEach(async (to, from, next) => {
  if (to.meta.requiresAuth === false) {
    return next()
  }

  const token = useMyCookies.get('token')
  if (!token) {
    return next({ name: 'Login', query: { redirect: to.fullPath } })
  }

  const userInfo = await useMyStorage.get('userInfo')
  if (!userInfo) {
    useUserStore().fetchUserInfo()
  }

  next()
})

router.afterEach((to) => {
  // 基本名称使用环境变量 VITE_TITLE，路由名称使用 meta.title
  const baseTitle = import.meta.env.VITE_TITLE || ''
  const pageTitle = to.meta.title as string || to.name as string || ''

  // 如果有页面标题，则格式为：页面标题 - 基本标题
  // 如果没有页面标题，则只显示基本标题
  const title = pageTitle ? `${pageTitle} - ${baseTitle}` : baseTitle

  useTitle(title)
})
