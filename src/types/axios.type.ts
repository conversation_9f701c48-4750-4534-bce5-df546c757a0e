/**
 * 分页请求参数接口
 * 用于定义分页请求参数的结构
 */
export interface ListRequestParams {
  // 当前页码
  page: number
  // 每页数量
  limit: number
  /**
   * 排序规则
   * 单字段格式：字段名：方向（例：created_at:desc）
   * 方向可选值：asc（默认，可省略） / desc
   * 多字段排序：用英文逗号分隔多个规则（例：sort=priority:desc,created_at:asc,title）
   */
  sort?: string
  // 其他可能的字段
  [key: string]: any
}

/**
 * 分页响应接口
 * 用于定义分页数据的结构
 */
export interface ListResponse<T> {
  // 列表数据
  list: T[]
  // 总数
  total: number
  // 当前页码
  page: number
  // 每页数量
  limit: number
}
