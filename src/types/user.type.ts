/**
 * 用户信息接口
 * 用于定义用户数据的结构
 */
export interface UserInfo {
  // 用户 ID
  userId: string
  // 用户名
  userName: string
  // 用户昵称
  nickName: string
  // 邮箱
  email: string
  // 手机号码
  phoneNumber: string
  // 性别
  sex: string
  // 最后登录日期
  lastLoginDate: string
  // 部门
  dept: string
  // 角色
  role: any | null
  // 第三方授权信息
  auths: AuthItem[]
  // 系统网址
  url: string
  // 用户头像
  avatar: string | null
  // 其他可能的字段
  [key: string]: any
}

/**
 * 授权项接口
 * 用于定义第三方授权数据的结构
 */
export interface AuthItem {
  // 第三方平台类型，如 'wechat_open', 'dingtalk'
  source: ThirdPartySource
  // 第三方平台返回的用户 ID
  authId: string
  // 第三方平台昵称
  userName?: string
  // 第三方平台头像
  avatar: string
  // 绑定时间
  createDate: string
  // 其他可能的字段
  [key: string]: any
}
