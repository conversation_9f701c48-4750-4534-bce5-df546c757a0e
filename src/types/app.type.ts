/**
 * 应用基础项
 */
export interface BaseAppItem {
  // 应用 ID
  appId: string
  // 应用名称
  appName: string
  // 应用 logo 图标
  logo: string | null
  // 应用图标
  icon: string
  // 应用图标主题色
  color:
    | 'teal'
    | 'purple'
    | 'sky'
    | 'yellow'
    | 'rose'
    | 'indigo'
    | 'pink'
    | 'blue'
    | 'green'
    | 'orange'
    | 'red'
    | 'amber'
    | 'lime'
    | 'emerald'
    | 'cyan'
    | 'violet'
    | 'fuchsia'
  // 跳转地址
  redirectUrl: string
  // 点击次数
  clickNum: number
  // 是否已绑定
  isBind: '1' | '0'
  // 点击最后日期
  clickLastDate: string | null
  /**
   * 绑定类型
   * '1': 账号密码绑定
   * '2': 手机号绑定
   * '3': oauth 鉴权跳转 - 不拼接 code
   */
  bindType: '1' | '2' | '3'
}

/**
 * 应用项
 */
export interface AppItem extends BaseAppItem {
  // 绑定用户
  bindUser: string | null
  // 绑定用户列表
  bindUserList: string[]
  // 应用详情
  detail: string
  // 应用分组
  appGroup: string
  /**
   * 对接状态
   * @description 不再使用
   * '1': 可对接
   * '2': 对接中
   * '3': 已对接
   */
  dockStatus: '1' | '2' | '3'
  /**
   * 应用级别
   * '9': 常用应用
   * '4': 国家级应用
   * '3': 省级应用
   * '2': 市级应用
   * '1': 区级应用
   * '0': 校级应用
   */
  level: '0' | '1' | '2' | '3' | '4' | '9'
  /**
   * 是否收藏
   * '1': 收藏
   * '0': 未收藏
   */
  isTop: '0' | '1'
  // 应用类型
  appType: string
  // 模块
  module: string | null
}

/**
 * 功能项
 */
export interface FunctionItem extends BaseAppItem {
  // 功能 ID - functionId
  id: string
  // 功能名称
  name: string
  /**
   * 功能类型
   * '1': 教育管理
   * '2': 学生成长
   * '3': 教师成长
   * '4': 学校发展
   * '5': 教学教研
   * '6': 数据中心
   */
  type: '1' | '2' | '3' | '4' | '5' | '6'
  // 统计名称
  statName?: string | null
  // 统计单位
  statUnit?: string | null
  // 统计值
  statValue?: string | number | null
}

// 绑定类型 '1': 账号密码绑定 请求参数
export interface BindUserParams {
  // 应用 ID
  appId: string
  // 账号
  name: string
  // 密码
  password: string
}

// 绑定类型 '2': 手机号绑定 请求参数
export interface BindUserByPhoneParams {
  // 应用 ID
  appId: string
  // 手机号，使用 UserInfo.phoneNumber
  phone: string
}

/**
 * ApiCheckBindAndGetUrl 接口的响应数据类型
 */
export interface CheckBindAndGetUrlResponse {
  // 授权码
  code: string
  // 基础重定向 URL (不含 code) - 注意 app 和 function 的链接不同
  redirectUrl: string
  // 当前应用对于该用户是否已绑定
  isBindUser: boolean
  // 应用 ID
  appId: string
  /**
   * 绑定类型
   * '1': 账号密码绑定
   * '2': 手机号绑定
   * '3': oauth 鉴权跳转 - 不拼接 code
   */
  bindType: '1' | '2' | '3'
  // 完整的重定向 URL (已拼接 code)
  fullRedirectUrl: string
}
