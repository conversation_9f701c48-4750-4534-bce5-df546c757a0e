/**
 * 获取短信验证码参数
 */
export interface SmsCodeParams {
  mobile: string
  type: 'login' | 'bind'
}

/**
 * 密码登录参数
 */
export interface LoginByPasswordParams {
  // 用户名（手机号）
  username: string
  // 密码
  password: string
  // 图形验证码的唯一标识
  uuid: string
  // 图形验证码
  code: string
}

/**
 * 验证码登录参数
 */
export interface LoginBySmsCodeParams {
  // 用户名（手机号）
  username: string
  // 短信验证码
  smsCode: string
}

/**
 * 第三方登录源类型
 * - 'wechat_open': 微信开放平台登录
 * - 'dingtalk': 钉钉登录
 */
export type ThirdPartySource = 'wechat_open' | 'dingtalk'

/**
 * 第三方扫码登录参数
 */
export interface LoginByThirdParams {
  // 登录来源
  source: ThirdPartySource
  // OAuth 授权码
  code: string
  // 用于防止 CSRF 攻击的状态参数
  state: string
}

/**
 * 第三方绑定登录参数
 */
export interface LoginByBindThirdParams {
  // 用户名（手机号）
  username?: string
  // 短信验证码
  smsCode?: string
  // 第三方登录参数
  authUser?: AuthUser
}

/**
 * 认证用户信息
 */
export interface AuthUser {
  // 昵称
  nickname: string
  // 头像
  avatar: string
  // 第三方来源
  source: ThirdPartySource
  // 第三方用户 ID
  uuid: string
  // 邮箱
  email: string | null
  // 用户名
  username: string
}

/**
 * 图形验证码响应
 */
export interface CaptchaImageResponse {
  // 图形验证码的唯一标识
  uuid: string
  // 图形验证码图片数据 (Base64 编码)
  img: string
  // 是否开启了图形验证码验证
  captchaOnOff: boolean
}

/**
 * 手机号绑定状态检查响应
 */
export interface CheckMobileBindResponse {
  // 是否绑定了微信开放平台账号
  wechat_open: boolean
  // 是否绑定了钉钉账号
  dingtalk: boolean
}
