/**
 * 通知公告
 */
export interface NoticeItem {
  // 通知 ID
  noticeId: number
  // 通知标题
  noticeTitle: string
  // 通知类型：'1' 个人消息 '2' 系统公告
  noticeType: '1' | '2'
  // 通知内容
  noticeContent: string
  // 创建人
  createBy: string
  // 创建日期
  createDate: string
  // 是否已读
  isRead: '1' | '0'
  // 应用 ID
  appId: string | null
  // 签名
  sign: string
  // 跳转链接
  linkUrl: string | null
  // 是否是链接
  isLink: string | null
  // 阅读数量
  readNum: number
  // 对应应用功能
  appFunction: FunctionItem | null
}

/**
 * 常见问题接口
 */
export interface FaqItem {
  // 问题 ID
  id: number
  // 问题标题
  question: string
  // 问题答案
  answer: string
}
