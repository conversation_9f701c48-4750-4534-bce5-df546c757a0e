<script setup lang="ts">
/**
 * 显示一个可拖动的浮动二维码缩略图，带有移动端入口链接
 * 将组件位置缓存在 localStorage 中，并实现边缘吸附及悬浮放大功能
 */

// 用于在 localStorage 中存储位置的键名
const STORAGE_KEY = 'floating-qrcode-position'
// 触发边缘吸附的距离（像素）
const SNAP_THRESHOLD = 50
// 放大后二维码容器的尺寸（像素）
const MAGNIFIED_QR_SIZE = 210
// 组件与放大二维码之间的间隙（像素）
const GAP_BETWEEN_MAIN_AND_MAGNIFIED = 10

const qrCodeElement = ref<HTMLElement | null>(null)
const position = ref({ top: window.innerHeight - 150, left: window.innerWidth - 200 })
const isDragging = ref(false)
const dragStartOffset = { x: 0, y: 0 }
const isHovering = ref(false)
const magnifiedSide = ref<'left' | 'right'>('right')

/**
 * 计算并应用边缘吸附效果
 * @param currentTop - 当前顶部位置
 * @param currentLeft - 当前左侧位置
 * @param elementWidth - 元素宽度
 * @param elementHeight - 元素高度
 * @returns {{ top: number, left: number }} 吸附后的位置
 */
function applySnap(currentTop: number, currentLeft: number, elementWidth: number, elementHeight: number): { top: number, left: number } {
  let newTop = currentTop
  let newLeft = currentLeft
  if (currentTop < SNAP_THRESHOLD)
    newTop = 0
  else if (window.innerHeight - (currentTop + elementHeight) < SNAP_THRESHOLD)
    newTop = window.innerHeight - elementHeight
  if (currentLeft < SNAP_THRESHOLD)
    newLeft = 0
  else if (window.innerWidth - (currentLeft + elementWidth) < SNAP_THRESHOLD)
    newLeft = window.innerWidth - elementWidth
  return { top: newTop, left: newLeft }
}

/**
 * 处理鼠标按下事件，开始拖动
 * @param event - 鼠标事件对象
 */
function onMouseDown(event: MouseEvent) {
  if (!qrCodeElement.value)
    return
  isDragging.value = true
  isHovering.value = false // 拖动开始时强制关闭悬浮状态
  qrCodeElement.value.style.cursor = 'grabbing'
  const rect = qrCodeElement.value.getBoundingClientRect()
  dragStartOffset.x = event.clientX - rect.left
  dragStartOffset.y = event.clientY - rect.top
  window.addEventListener('mousemove', onMouseMove)
  window.addEventListener('mouseup', onMouseUp)
}

/**
 * 处理鼠标移动事件，更新组件位置
 * @param event - 鼠标事件对象
 */
function onMouseMove(event: MouseEvent) {
  if (!isDragging.value || !qrCodeElement.value)
    return

  isHovering.value = false // 拖动过程中强制关闭悬浮状态

  let newTop = event.clientY - dragStartOffset.y
  let newLeft = event.clientX - dragStartOffset.x
  const elementWidth = qrCodeElement.value.offsetWidth
  const elementHeight = qrCodeElement.value.offsetHeight
  newTop = Math.max(0, Math.min(newTop, window.innerHeight - elementHeight))
  newLeft = Math.max(0, Math.min(newLeft, window.innerWidth - elementWidth))
  const snappedPosition = applySnap(newTop, newLeft, elementWidth, elementHeight)
  position.value = snappedPosition
}

/**
 * 处理鼠标松开事件，结束拖动并保存位置
 */
function onMouseUp() {
  if (!isDragging.value || !qrCodeElement.value)
    return
  isDragging.value = false

  if (qrCodeElement.value)
    qrCodeElement.value.style.cursor = 'grab'
  const elementWidth = qrCodeElement.value.offsetWidth
  const elementHeight = qrCodeElement.value.offsetHeight
  const snappedPosition = applySnap(position.value.top, position.value.left, elementWidth, elementHeight)
  position.value = snappedPosition
  localStorage.setItem(STORAGE_KEY, JSON.stringify(position.value))
  window.removeEventListener('mousemove', onMouseMove)
  window.removeEventListener('mouseup', onMouseUp)
}

/**
 * 从 localStorage 加载保存的位置
 */
function loadPosition() {
  const savedPosition = localStorage.getItem(STORAGE_KEY)
  if (savedPosition) {
    try {
      const parsedPosition = JSON.parse(savedPosition)
      if (typeof parsedPosition.top === 'number' && typeof parsedPosition.left === 'number') {
        if (qrCodeElement.value) {
          const elementWidth = qrCodeElement.value.offsetWidth
          const elementHeight = qrCodeElement.value.offsetHeight
          parsedPosition.top = Math.max(0, Math.min(parsedPosition.top, window.innerHeight - elementHeight))
          parsedPosition.left = Math.max(0, Math.min(parsedPosition.left, window.innerWidth - elementWidth))
        }
        position.value = parsedPosition
      }
      else {
        localStorage.removeItem(STORAGE_KEY)
        setDefaultPosition()
      }
    }
    catch (error) {
      console.error('解析保存的位置失败:', error)
      localStorage.removeItem(STORAGE_KEY)
      setDefaultPosition()
    }
  }
  else {
    setDefaultPosition()
  }
}

/**
 * 设置组件的默认位置
 */
function setDefaultPosition() {
  if (qrCodeElement.value) {
    const elementWidth = qrCodeElement.value.offsetWidth
    const elementHeight = qrCodeElement.value.offsetHeight
    position.value = {
      top: Math.max(0, window.innerHeight - elementHeight - 100),
      left: Math.max(0, window.innerWidth - elementWidth - 100),
    }
  }
  else {
    // 元素不可用时的备用方案
    position.value = { top: window.innerHeight - 150, left: window.innerWidth - 200 }
  }
}

/**
 * 处理窗口大小改变事件，调整组件位置以适应新窗口尺寸
 */
function handleResize() {
  if (!qrCodeElement.value)
    return

  // 窗口尺寸变化时重置为默认位置
  setDefaultPosition()

  // 确保默认位置在可视范围内
  const elementWidth = qrCodeElement.value.offsetWidth
  const elementHeight = qrCodeElement.value.offsetHeight
  let newTop = position.value.top
  let newLeft = position.value.left

  newTop = Math.max(0, Math.min(newTop, window.innerHeight - elementHeight))
  newLeft = Math.max(0, Math.min(newLeft, window.innerWidth - elementWidth))

  // 应用边缘吸附效果
  const snappedPosition = applySnap(newTop, newLeft, elementWidth, elementHeight)
  position.value = snappedPosition
  localStorage.setItem(STORAGE_KEY, JSON.stringify(position.value))
}

/**
 * 处理鼠标进入事件，准备显示放大的二维码
 */
function handleMouseEnter() {
  if (isDragging.value || !qrCodeElement.value)
    return // 如果正在拖动，则不触发悬浮
  isHovering.value = true
  const rect = qrCodeElement.value!.getBoundingClientRect()
  const viewportWidth = window.innerWidth

  const spaceOnLeft = rect.left
  const spaceOnRight = viewportWidth - rect.right

  const canPlaceLeft = spaceOnLeft >= MAGNIFIED_QR_SIZE + GAP_BETWEEN_MAIN_AND_MAGNIFIED
  const canPlaceRight = spaceOnRight >= MAGNIFIED_QR_SIZE + GAP_BETWEEN_MAIN_AND_MAGNIFIED

  if (canPlaceLeft) {
    magnifiedSide.value = 'left'
  }
  else if (canPlaceRight) {
    magnifiedSide.value = 'right'
  }
  else {
    // 放置在空间较多的一侧，如果空间相等则放置在左侧
    magnifiedSide.value = spaceOnLeft >= spaceOnRight ? 'left' : 'right'
  }
}

/**
 * 处理鼠标离开事件，隐藏放大的二维码
 */
function handleMouseLeave() {
  isHovering.value = false
}

/**
 * 计算放大二维码的样式
 * @returns {Record<string, string>} 放大二维码的样式对象
 */
const magnifiedStyle = computed(() => {
  if (!qrCodeElement.value || !isHovering.value || isDragging.value)
    return {} // 拖动时不显示放大二维码

  const mainElementRect = qrCodeElement.value.getBoundingClientRect()
  const mainHeight = qrCodeElement.value.offsetHeight

  // 计算放大二维码的绝对顶部位置，与主组件垂直居中
  let desiredAbsoluteTop = mainElementRect.top + (mainHeight - MAGNIFIED_QR_SIZE) / 2

  // 确保放大二维码在视口内垂直居中，保留间隙
  desiredAbsoluteTop = Math.max(GAP_BETWEEN_MAIN_AND_MAGNIFIED, desiredAbsoluteTop)
  desiredAbsoluteTop = Math.min(desiredAbsoluteTop, window.innerHeight - MAGNIFIED_QR_SIZE - GAP_BETWEEN_MAIN_AND_MAGNIFIED)

  // Convert absolute top to relative top for CSS positioning
  const relativeTop = desiredAbsoluteTop - mainElementRect.top

  const style: Record<string, string> = {
    top: `${relativeTop}px`,
    width: `${MAGNIFIED_QR_SIZE}px`,
    height: `${MAGNIFIED_QR_SIZE}px`,
  }

  if (magnifiedSide.value === 'left') {
    style.left = `-${MAGNIFIED_QR_SIZE + GAP_BETWEEN_MAIN_AND_MAGNIFIED}px`
  }
  else {
    style.left = `${qrCodeElement.value.offsetWidth + GAP_BETWEEN_MAIN_AND_MAGNIFIED}px`
  }
  return style
})

onMounted(() => {
  // 确保 qrCodeElement 可用后再访问其属性
  nextTick(() => {
    if (qrCodeElement.value) {
      loadPosition() // 先加载位置

      // 然后根据实际元素尺寸调整和吸附
      const currentTop = position.value.top
      const currentLeft = position.value.left
      const elementWidth = qrCodeElement.value.offsetWidth
      const elementHeight = qrCodeElement.value.offsetHeight

      let adjustedTop = Math.max(0, Math.min(currentTop, window.innerHeight - elementHeight))
      let adjustedLeft = Math.max(0, Math.min(currentLeft, window.innerWidth - elementWidth))

      const snappedPosition = applySnap(adjustedTop, adjustedLeft, elementWidth, elementHeight)
      adjustedTop = snappedPosition.top
      adjustedLeft = snappedPosition.left

      if (adjustedTop !== currentTop || adjustedLeft !== currentLeft) {
        position.value = { top: adjustedTop, left: adjustedLeft }
        localStorage.setItem(STORAGE_KEY, JSON.stringify(position.value))
      }
    }
    else {
      // 挂载时元素不可用的备用方案
      setDefaultPosition()
    }
  })
  window.addEventListener('resize', handleResize)
})

onBeforeUnmount(() => {
  window.removeEventListener('mousemove', onMouseMove)
  window.removeEventListener('mouseup', onMouseUp)
  window.removeEventListener('resize', handleResize)
})
</script>

<template>
  <div
    ref="qrCodeElement"
    class="fixed z-[1000] w-20 flex flex-col cursor-grab items-center gap-y-1 border border-gray-200 rounded-lg bg-white p-2 shadow-xl dark:border-gray-700 dark:bg-gray-800"
    :style="{ top: `${position.top}px`, left: `${position.left}px` }"
    @mousedown="onMouseDown"
    @mouseenter="handleMouseEnter"
    @mouseleave="handleMouseLeave"
  >
    <img src="/qrcode.jpg" alt="QR Code Thumbnail" class="pointer-events-none size-16 select-none rounded-md object-cover">
    <span class="text-xs text-gray-500 dark:text-gray-300">手机端入口</span>
    <Transition name="fade-magnified">
      <div
        v-if="isHovering && !isDragging"
        :style="magnifiedStyle"
        class="absolute z-[1001] border border-gray-300 rounded-lg bg-white p-2 shadow-2xl dark:border-gray-600 dark:bg-gray-900"
      >
        <img src="/qrcode.jpg" alt="Magnified QR Code" class="h-48 w-48 select-none rounded object-cover">
      </div>
    </Transition>
  </div>
</template>

<style scoped>
.fade-magnified-enter-active,
.fade-magnified-leave-active {
  transition: opacity 0.2s ease-in-out;
}
.fade-magnified-enter-from,
.fade-magnified-leave-to {
  opacity: 0;
}
</style>
