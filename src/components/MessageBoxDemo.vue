<script lang="ts" setup>
import { ElMessageBox } from 'element-plus'

function open() {
  ElMessageBox.alert('This is a message', 'Title', {
    // if you want to disable its autofocus
    // autofocus: false,
    confirmButtonText: 'OK',
    // callback: (action: Action) => {
    //   ElMessage({
    //     type: 'info',
    //     message: `action: ${action}`,
    //   })
    // },
  })
}
</script>

<template>
  <ElButton plain @click="open">
    Click to open the Message Box
  </ElButton>
</template>
