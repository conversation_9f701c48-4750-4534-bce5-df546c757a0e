<script setup lang="ts">
interface Props {
  title?: string
}

const props = withDefaults(defineProps<Props>(), {
  title: '',
})

defineSlots<{
  default: (props) => any
  more: (props) => any
  footer: (props) => any
}>()
</script>

<template>
  <div class="overflow-hidden rounded-lg bg-white shadow-sm divide-y">
    <div class="flex items-center justify-between p-5">
      <h2 class="text-lg font-medium">
        {{ title }}
      </h2>
      <div v-if="$slots.more" class="cursor-pointer text-sm text-gray-500 hover:text-gray-700">
        <slot name="more" />
      </div>
    </div>
    <slot />
    <div v-if="$slots.footer" class="cursor-pointer p-4 text-center text-sm text-gray-500 hover:text-gray-700">
      <slot name="footer" />
    </div>
  </div>

  <!-- <div class="overflow-hidden rounded-lg bg-white shadow divide-y divide-gray-200">
    <div class="px-4 py-5 sm:p-6" />
    <div class="px-4 py-4 sm:px-6" />
  </div> -->

  <!-- <div class="mb-6 overflow-hidden border rounded-lg bg-white shadow-sm">
    <div class="border-b border-gray-200 bg-white px-4 py-5 sm:px-6">
      <div class="flex flex-wrap items-center justify-between -ml-4 -mt-2 sm:flex-nowrap">
        <div class="ml-4 mt-2">
          <h3 class="text-lg text-gray-800 font-semibold leading-6">
            {{ title }}
          </h3>
        </div>
        <div class="ml-4 mt-2 flex-shrink-0">
          <slot name="more" />
        </div>
      </div>
    </div>
    <slot />
  </div> -->
</template>
