<script setup lang="ts">
import { DisclosureButton } from '@headlessui/vue'

// 导航项接口
interface NavItem {
  name: string
  routeName: string
}

// 用户导航项接口
interface UserNavItem {
  name: string
  routeName: string
  isLogout?: boolean
}

interface Props {
  isMobile?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  isMobile: false,
})

// 导航菜单数据
const navigation = ref<NavItem[]>([
  { name: '工作台', routeName: 'Dashboard' },
  { name: '应用中心', routeName: 'Apps' },
  { name: '通知公告', routeName: 'Notices' },
  { name: '个人中心', routeName: 'Settings' },
])

// 用户导航菜单数据
const userNavigation = ref<UserNavItem[]>([
  { name: '退出登录', routeName: '', isLogout: true },
])

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()
const userStore = useUserStore()
const userInfo = computed(() => userStore.userInfo)

// 当前激活的导航项
const activeItem = ref<string | null>(null)

/**
 * 判断导航项是否为当前激活项
 * @param item - 导航项
 */
function isActive(item: NavItem) {
  // 如果导航项没有路由名称，则不可能是当前项
  if (!item.routeName)
    return false

  return activeItem.value === item.routeName
}

/**
 * 更新当前激活的导航项
 */
function updateActiveItem() {
  // 获取当前路由路径的第一段
  const currentPath = route.path.split('/')[1] || ''

  // 遍历导航项，查找匹配的路由
  for (const item of navigation.value) {
    if (!item.routeName)
      continue

    // 获取目标路由的完整路径信息
    const targetRoute = router.resolve({ name: item.routeName })
    // 提取目标路由的第一段路径
    const targetPath = targetRoute.path.split('/')[1] || ''

    // 比较路径第一段是否一致
    if (currentPath === targetPath) {
      activeItem.value = item.routeName
      return
    }
  }

  activeItem.value = null
}

// 监听路由变化，更新激活项
watch(() => route.path, updateActiveItem, { immediate: true })

/**
 * 处理导航项点击
 * @param item - 导航项
 */
function handleNavClick(item: NavItem) {
  if (item.routeName) {
    router.push({ name: item.routeName })
  }
}

/**
 * 处理用户导航点击
 * @param item - 用户导航项
 */
async function handleUserNavClick(item: UserNavItem) {
  if (item.isLogout) {
    await handleLogout()
    return
  }

  if (item.routeName) {
    router.push({ name: item.routeName })
  }
}

function handleLogout() {
  authStore.logout()
}
</script>

<template>
  <!-- 桌面端导航 -->
  <div v-if="!isMobile" class="ml-10 flex items-baseline space-x-4">
    <a
      v-for="item in navigation" :key="item.name" class="cursor-pointer rounded-md px-3 py-2 text-sm font-medium"
      :class="[isActive(item) ? 'bg-blue-700 text-white' : 'text-gray-200 hover:bg-blue-600 hover:text-white']"
      :aria-current="isActive(item) ? 'page' : undefined" @click="handleNavClick(item)"
    >
      {{ item.name }}
    </a>
  </div>

  <!-- 移动端导航 -->
  <div v-else>
    <!-- 主导航 -->
    <div class="px-2 py-3 space-y-1 sm:px-3">
      <a
        v-for="item in navigation" :key="item.name"
        class="block cursor-pointer rounded-md px-3 py-2 text-base font-medium"
        :class="[isActive(item) ? 'bg-blue-700 text-white' : 'text-gray-200 hover:bg-blue-600 hover:text-white']"
        :aria-current="isActive(item) ? 'page' : undefined" @click="handleNavClick(item)"
      >
        {{ item.name }}
      </a>
    </div>

    <!-- 用户信息 -->
    <div class="z-1 border-t border-gray-100 pb-3 pt-4">
      <div v-if="userInfo" class="flex items-center px-5">
        <div class="flex-shrink-0">
          <img class="h-10 w-10 rounded-full" :src="userInfo.avatar" alt="">
        </div>
        <div class="ml-3">
          <div class="text-base text-white font-medium leading-none">
            {{ userInfo.nickName }}
          </div>
        </div>
      </div>
      <div class="mt-3 px-2 space-y-1">
        <DisclosureButton
          v-for="item in userNavigation" :key="item.name" as="a" href="javascript:;"
          class="block rounded-md px-3 py-2 text-base text-white font-medium hover:bg-blue-700 hover:text-white"
          @click="handleUserNavClick(item)"
        >
          {{ item.name }}
        </DisclosureButton>
      </div>
    </div>
  </div>
</template>
