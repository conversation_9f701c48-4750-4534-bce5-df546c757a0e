<script setup lang="ts">
import { Disclosure, DisclosureButton, DisclosurePanel } from '@headlessui/vue'

// 获取用户信息
const userStore = useUserStore()
const authStore = useAuthStore()
const router = useRouter()
const userInfo = computed(() => userStore.userInfo)

// 用户导航菜单数据
const userNavigation = [
  { name: '退出登录', routeName: '', isLogout: true, icon: 'i-heroicons:power' },
  { name: '个人中心', routeName: 'Settings', icon: 'i-heroicons:user' },
]

/**
 * 处理用户导航点击
 */
function handleUserNavClick(item: { name: string, routeName: string, isLogout?: boolean }) {
  if (item.isLogout) {
    return authStore.logout()
  }

  if (item.routeName) {
    router.push({ name: item.routeName })
  }
}
</script>

<template>
  <div class="min-h-full">
    <div class="bg-blue-600 pb-32">
      <Disclosure v-slot="{ open }" as="nav" class="fixed z-100 w-full bg-blue-600">
        <div class="mx-auto max-w-90rem lg:px-8 sm:px-6">
          <div class="border-gray-100">
            <div class="h-16 flex items-center justify-between px-4 sm:px-0">
              <div class="flex items-center">
                <div class="flex flex-shrink-0 cursor-pointer items-center" @click="router.push({ name: 'Dashboard' })">
                  <img class="h-14 w-14" src="@/assets/favicon.ico">
                  <span class="ml-2 text-xl text-white font-bold"> {{ useEnv.VITE_TITLE }}</span>
                </div>
                <div class="hidden lg:block">
                  <Navigation />
                </div>
              </div>
              <div class="hidden lg:block">
                <div class="ml-4 flex items-center lg:ml-6">
                  <!-- 用户头像和退出按钮 -->
                  <div class="flex items-center space-x-3">
                    <!-- 用户头像 -->
                    <div class="group flex cursor-pointer items-center" @click="handleUserNavClick(userNavigation[1])">
                      <img
                        v-if="userInfo?.avatar" :src="userInfo.avatar"
                        class="h-8 w-8 border-2 border-white rounded-full object-cover" :alt="userInfo.nickName"
                      >
                      <i v-else class="i-heroicons:user h-7 w-7 text-white" aria-hidden="true" />
                      <span class="ml-2 text-sm text-white transition-colors group-hover:text-gray-200">{{ userInfo?.nickName }}</span>
                    </div>

                    <!-- 退出登录按钮 -->
                    <button
                      class="flex items-center text-white transition-transform hover:scale-105 hover:text-gray-200 focus:outline-none"
                      @click="handleUserNavClick(userNavigation[0])"
                    >
                      <i class="i-heroicons:power h-6 w-6" aria-hidden="true" />
                    </button>
                  </div>
                </div>
              </div>
              <div class="flex -mr-2 lg:hidden">
                <!-- Mobile menu button -->
                <DisclosureButton
                  class="inline-flex items-center justify-center rounded-md bg-blue-600 p-2 text-white hover:bg-blue-700 hover:text-white"
                >
                  <i v-if="!open" class="i-heroicons:bars-3 block size-6" aria-hidden="true" />
                  <i v-else class="i-heroicons:x-mark block h-6 w-6" aria-hidden="true" />
                </DisclosureButton>
              </div>
            </div>
          </div>
        </div>

        <DisclosurePanel class="border-b border-gray-100 lg:hidden">
          <Navigation :is-mobile="true" />
        </DisclosurePanel>
      </Disclosure>
      <header class="pb-6 pt-20">
        <div class="mx-auto max-w-90rem px-4 lg:px-8 sm:px-6">
          <Banner />
        </div>
      </header>
    </div>

    <main class="-mt-32">
      <div class="mx-auto max-w-90rem px-4 pb-12 lg:px-8 sm:px-6">
        <!-- Your content -->
        <RouterView />
      </div>
    </main>
  </div>
</template>
