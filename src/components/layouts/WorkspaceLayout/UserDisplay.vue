<script setup>
// 获取用户信息
const userStore = useUserStore()
const userInfo = computed(() => userStore.userInfo)

// 在组件挂载时获取用户信息
onMounted(async () => {
  // if (!userInfo.value) {
  // await userStore.fetchUserInfo()
  // }
})

// 用户信息数据
const user = computed(() => ({
  ...userInfo.value,
  isPro: true, // 可以根据实际情况设置
}))

const userFields = computed(() => [
  { key: '部门', value: userInfo.value?.dept || '未知' },
  { key: '电话', value: userInfo.value?.phoneNumber || '未知' },
  { key: '邮箱', value: userInfo.value?.email || '未知' },
])
</script>

<template>
  <div class="w-full flex items-center rounded-md">
    <!-- 用户头像 -->
    <div class="relative">
      <img
        :src="user.avatar"
        alt="用户头像"
        class="h-20 w-20 border-2 border-white rounded-lg object-cover"
      >
      <!-- PRO 标签 -->
      <div v-if="user.isPro" class="absolute left-1/2 rounded-md bg-green-500 px-2 py-0.5 text-xs text-white font-bold -bottom-1 -translate-x-1/2">
        在线
      </div>
    </div>

    <!-- 用户信息 -->
    <div class="ml-4 flex-1">
      <h2 class="text-2xl text-white font-bold">
        {{ user.nickName }}
      </h2>
      <div class="mt-1 flex flex-col text-sm md:flex-row md:items-center">
        <template v-for="(field, index) in userFields" :key="index">
          <span class="flex items-center">
            <span class="text-gray-100 font-medium">{{ field.key }}:</span>
            <span class="ml-1 text-white">{{ field.value }}</span>
          </span>
          <span v-if="index < userFields.length - 1" class="mx-2 hidden text-gray-100 md:inline">•</span>
        </template>
      </div>
    </div>

    <!-- 修改个人信息按钮 -->
    <button class="flex items-center rounded-md bg-gray-700 px-4 py-2 text-sm text-white transition-colors duration-200 hover:bg-gray-600">
      <svg xmlns="http://www.w3.org/2000/svg" class="mr-1 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
      </svg>
      修改个人信息
    </button>
  </div>
</template>
