import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import { Icon } from '@iconify/vue'
import { createPinia } from 'pinia'
import { createApp } from 'vue'
import App from './App.vue'

import router from './router/index'
import 'virtual:uno.css'
import '@unocss/reset/tailwind.css'
import 'element-plus/theme-chalk/src/index.scss'
// import 'element-plus/theme-chalk/src/message.scss'
// import 'element-plus/theme-chalk/src/message-box.scss'

import '~/styles/index.scss'

const app = createApp(App)
const pinia = createPinia()

// 导入 Element Plus 所有图标并进行全局注册（不优先使用）
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}
// 导入 Iconify 图标组件并进行全局注册
app.component('Icon', Icon)

// 埋点统计
useUmami(import.meta.env.VITE_CODE)

app.use(pinia)
app.use(router)
app.mount('#app')
