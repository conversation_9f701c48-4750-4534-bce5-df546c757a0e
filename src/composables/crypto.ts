import JSEncrypt from 'jsencrypt'

interface Crypto {
  encrypt: (txt: string) => string | false
  decrypt: (txt: string) => string | false
}

function createEncrypt(publicKey: string) {
  const encrypt = new JSEncrypt()
  encrypt.setPublicKey(publicKey)
  return (txt: string) => encrypt.encrypt(txt)
}

function createDecrypt(privateKey: string) {
  const decrypt = new JSEncrypt()
  decrypt.setPrivateKey(privateKey)
  return (txt: string) => decrypt.decrypt(txt)
}

export const useCrypto: Crypto = {
  encrypt: createEncrypt(import.meta.env.VITE_PUBLIC_KEY),
  decrypt: createDecrypt(import.meta.env.VITE_PRIVATE_KEY),
}
