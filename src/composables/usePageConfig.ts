import { useMyStorage } from '@/composables/storage'

const STORAGE_KEY = 'config'

/**
 * 管理页面特有的配置，包括从缓存加载和监听变化以保存
 *
 * @param pageKey - 当前页面的唯一标识符，用于在缓存中区分不同页面的配置
 * @param state - 一个响应式对象 (reactive)，其属性的变更将被监听和持久化
 * @param onLoaded - (可选) 从缓存加载配置后执行的回调函数
 */
export function usePageConfig<T extends object>(pageKey: string, state: T, onLoaded?: () => void) {
  /**
   * 从缓存中加载配置并应用到响应式状态
   */
  const loadConfig = async () => {
    const config = await useMyStorage.getItem<Record<string, T>>(STORAGE_KEY)
    if (config && typeof config === 'object' && pageKey in config) {
      const pageConfig = config[pageKey]
      if (pageConfig && typeof pageConfig === 'object') {
        // 将缓存的配置应用到 state
        for (const key in pageConfig) {
          if (Reflect.has(state, key)) {
            (state as any)[key] = pageConfig[key]
          }
        }
      }
    }
    // 如果提供了回调，则执行
    if (onLoaded) {
      onLoaded()
    }
  }

  /**
   * 将当前配置保存到缓存中
   * @param newConfig - 最新的配置值
   */
  const saveConfig = async (newConfig: T) => {
    let config = await useMyStorage.getItem<Record<string, any>>(STORAGE_KEY)
    // 确保 config 是一个对象
    if (typeof config !== 'object' || config === null) {
      config = {}
    }
    const updatedConfig = { ...config, [pageKey]: newConfig }
    await useMyStorage.setItem(STORAGE_KEY, updatedConfig)
  }

  // 监听状态变化并保存
  watch(
    state,
    (newValue) => {
      saveConfig(newValue)
    },
    { deep: true },
  )

  // 组件挂载后加载配置
  onMounted(() => {
    loadConfig()
  })
}
