import Cookies from 'js-cookie'
import { createStorage } from 'unstorage'
import localStorageDriver from 'unstorage/drivers/localstorage'

const prefix = `${import.meta.env.VITE_SLUG}<${import.meta.env.VITE_ENV}>`

// 创建 localStorage 存储实例，统一添加应用前缀
export const useMyStorage = createStorage({
  driver: localStorageDriver({ base: prefix }),
})

// 封装 js-cookie，统一添加应用路径
export const useMyCookies = {
  get: (name: string | undefined) =>
    name ? Cookies.get(name) : Cookies.get(),
  set: (name: string, value: string, options?: any) =>
    Cookies.set(name, value, {
      expires: 1,
      path: import.meta.env.VITE_BASE_URL,
      ...options,
    }),
  remove: (name: string, options?: any) =>
    Cookies.remove(name, {
      path: import.meta.env.VITE_BASE_URL,
      ...options,
    }),
}
