import type { AxiosError, AxiosRequestConfig, AxiosResponse, InternalAxiosRequestConfig } from 'axios'
import axios from 'axios'
import { ElMessage } from 'element-plus'
import router from '@/router'

// 扩展请求配置接口，添加错误提示控制选项
export interface RequestConfig extends AxiosRequestConfig {
  showErrorMsg?: boolean
}

// 扩展内部请求配置接口，添加错误提示控制选项
export interface InternalRequestConfig extends InternalAxiosRequestConfig {
  showErrorMsg?: boolean
}

/**
 * 统一错误提示处理
 * @param code 错误码
 * @param message 错误信息
 * @param showErrorMsg 是否显示错误提示，默认为 true
 */
export function handleRequestError(code: number, message: string, showErrorMsg = true): void {
  switch (code) {
    case 400:
      showErrorMsg && ElMessage.error(message || '请求参数错误')
      break
    case 401:
      router.push({ name: 'Login', query: { redirect: router.currentRoute.value.path } })
      break
    case 403:
      showErrorMsg && ElMessage.error(message || '权限不足')
      break
    case 404:
      showErrorMsg && ElMessage.error(message || '请求的资源不存在')
      break
    case 408:
      showErrorMsg && ElMessage.error(message || '请求超时，请稍后重试')
      break
    case 500:
      showErrorMsg && ElMessage.error(message || '服务器内部错误')
      break
    case 502:
      showErrorMsg && ElMessage.error(message || '网关错误')
      break
    case 503:
      showErrorMsg && ElMessage.error(message || '服务暂时不可用')
      break
    case 504:
      showErrorMsg && ElMessage.error(message || '网关超时')
      break
    default:
      showErrorMsg && ElMessage.error(message || '未知错误')
  }
}

// 创建请求实例
export const useRequest = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || '/api',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// 请求拦截器
useRequest.interceptors.request.use(
  async (config: InternalRequestConfig) => {
    // 添加 AbortController 处理
    addPendingRequest(config)

    const token = await useMyCookies.get('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    config.headers.Code = import.meta.env.VITE_CODE

    // 过滤空值参数
    if (config.params && typeof config.params === 'object') {
      config.params = Object.fromEntries(
        Object.entries(config.params).filter(([_, v]) => v !== undefined && v !== null && v !== ''),
      )
    }

    return config
  },
  (error: any) => {
    ElMessage.error(error.message || '网络故障')
    return Promise.reject(error)
  },
)

// 响应拦截器
useRequest.interceptors.response.use(
  (response: AxiosResponse) => {
    // 请求完成后，从 pendingRequests 中移除
    removePendingRequest(response.config as InternalRequestConfig)

    // 若 response.data = { msg, code, data } 格式返回 response.data.data，若不是直接返回 response.data
    return response.data?.data ?? response.data
  },
  (error: any) => {
    // 如果是请求取消导致的错误，返回包含取消信息的 Error 对象
    if (axios.isCancel(error)) {
      const cancelError = error as AxiosError
      if (cancelError.config) {
        removePendingRequest(cancelError.config as InternalAxiosRequestConfig)
      }
      return Promise.reject(error)
    }

    // 请求发生错误时，也需要从 pendingRequests 中移除
    const config = (error.response?.config ?? error.config) as InternalRequestConfig
    if (config) {
      removePendingRequest(config)
    }

    // 错误日志输出
    console.group('%c请求错误', 'color: #f56c6c; font-weight: bold; ', `${error.response?.config?.method?.toUpperCase()} ${error.response?.config?.url}`)
    console.log(error.response?.data)
    console.groupEnd()

    // 获取错误状态码
    const status = error.response?.status || error.response?.data?.code || 400
    // 获取错误信息
    const message = error.response?.data?.detail || error.response?.data?.msg || '未知错误'
    // 获取请求配置中的错误提示控制选项
    const showErrorMsg = (error.config as InternalRequestConfig)?.showErrorMsg !== false

    // 统一错误提示处理
    handleRequestError(status, message, showErrorMsg)

    return Promise.reject(error.response?.data)
  },
)

// 用于存储请求的 Map，键为请求标识，值为 AbortController 实例
const pendingRequests = new Map<string, AbortController>()

/**
 * 生成请求标识
 * @param config 请求配置
 * @returns 请求标识
 */
function generateRequestId(config: InternalRequestConfig): string {
  const { method, url, params, data } = config
  return [
    method?.toLowerCase(),
    url,
    params
      ? JSON.stringify(Object.keys(params).sort().reduce(
          (acc, k) => ({ ...acc, [k]: (params as any)[k] }),
          {},
        ))
      : '',
    data ? JSON.stringify(data) : '',
  ].join('&')
}

/**
 * 添加请求到 pendingRequests
 * @param config 请求配置
 */
function addPendingRequest(config: InternalRequestConfig): void {
  const requestId = generateRequestId(config)

  // 如果存在相同请求，则取消之前的请求
  if (pendingRequests.has(requestId)) {
    const controller = pendingRequests.get(requestId)!
    controller.abort()
    pendingRequests.delete(requestId)
  }

  // 为当前请求创建 AbortController
  const controller = new AbortController()
  config.signal = controller.signal
  pendingRequests.set(requestId, controller)
}

/**
 * 移除 pendingRequests 中的请求
 * @param config 请求配置
 */
function removePendingRequest(config: InternalRequestConfig): void {
  const requestId = generateRequestId(config)
  if (pendingRequests.has(requestId)) {
    pendingRequests.delete(requestId)
  }
}
