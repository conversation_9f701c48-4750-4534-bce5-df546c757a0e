<script>
export default {
  name: 'TalentC<PERSON>',
  props: {
    talent: {
      type: Object,
      required: true,
    },
  },
}
</script>

<template>
  <div class="overflow-hidden rounded-lg bg-white shadow-sm">
    <div class="p-5">
      <!-- Avatar and name -->
      <div class="mb-4 flex items-start">
        <!-- Avatar or initials -->
        <div v-if="talent.avatar" class="relative mr-3">
          <img :src="talent.avatar" alt="Avatar" class="h-10 w-10 rounded-full object-cover">
          <span v-if="talent.isPro" class="absolute rounded bg-green-500 px-1 text-[10px] text-white uppercase -bottom-1 -right-1">Pro</span>
        </div>
        <div v-else class="relative mr-3">
          <div class="h-10 w-10 flex items-center justify-center rounded-full bg-gray-200 text-gray-600 font-medium">
            {{ talent.initials }}
          </div>
          <span v-if="talent.isPro" class="absolute rounded bg-green-500 px-1 text-[10px] text-white uppercase -bottom-1 -right-1">Pro</span>
        </div>

        <!-- Name and role -->
        <div>
          <h3 class="font-medium">
            {{ talent.name }}
          </h3>
          <p class="text-sm text-gray-500">
            {{ talent.role }}
          </p>
        </div>
      </div>

      <!-- Contact info -->
      <div class="text-sm space-y-2">
        <div v-if="talent.phone" class="flex items-center text-gray-600">
          <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-2 text-gray-400">
            <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z" />
          </svg>
          {{ talent.phone }}
        </div>
        <div class="flex items-center text-gray-600">
          <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-2 text-gray-400">
            <rect width="20" height="16" x="2" y="4" rx="2" />
            <path d="m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7" />
          </svg>
          {{ talent.email }}
        </div>
      </div>
    </div>
  </div>
</template>
