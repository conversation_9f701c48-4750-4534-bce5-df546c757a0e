<script>
export default {
  name: 'ProfileSetup',
  props: {
    profileData: {
      type: Object,
      required: true,
    },
  },
}
</script>

<template>
  <div class="mb-6 overflow-hidden rounded-lg bg-white shadow-sm">
    <!-- Header -->
    <div class="flex items-center justify-between border-b p-5">
      <h2 class="text-lg font-medium">
        Profile setup
      </h2>
      <span class="rounded bg-green-500 px-2 py-1 text-xs text-white uppercase">Pro</span>
    </div>

    <!-- Progress -->
    <div class="p-5">
      <div class="mb-2 flex items-center justify-between">
        <p class="text-sm text-gray-600">
          {{ profileData.completed }} of {{ profileData.total }} completed
        </p>
      </div>

      <!-- Progress bar -->
      <div class="mb-4 h-2 w-full overflow-hidden rounded-full bg-gray-200">
        <div
          class="h-full from-green-500 to-blue-500 bg-gradient-to-r"
          :style="`width: ${(profileData.completed / profileData.total) * 100}%`"
        />
      </div>

      <!-- Completion message -->
      <p class="mb-4 text-sm text-gray-600">
        Your profile needs to be at least <span class="font-medium">{{ profileData.requiredCompletion }}%
          complete</span> to be publicly visible.
      </p>

      <!-- Tasks list -->
      <ul class="space-y-3">
        <li
          v-for="task in profileData.tasks" :key="task.id"
          class="flex items-center justify-between border-b border-gray-100 py-2 last:border-0"
        >
          <div class="flex items-center">
            <div v-if="task.completed" class="mr-3 h-6 w-6 flex items-center justify-center rounded-full bg-green-500">
              <svg
                xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none"
                stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
              >
                <polyline points="20 6 9 17 4 12" />
              </svg>
            </div>
            <div v-else class="mr-3 h-6 w-6 border border-gray-300 rounded-full" />
            <span :class="{ 'text-gray-400 line-through': task.completed }">{{ task.title }}</span>
          </div>
          <button class="text-sm text-gray-500 hover:text-[#409eff]" :class="{ 'opacity-50': task.completed }">
            {{ task.action }}
          </button>
        </li>
      </ul>
    </div>
  </div>
</template>
