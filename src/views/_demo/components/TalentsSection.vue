<script>
import TalentCard from './TalentCard.vue'

export default {
  name: 'TalentsSection',
  components: {
    TalentCard,
  },
  props: {
    talents: {
      type: Object,
      required: true,
    },
  },
}
</script>

<template>
  <div class="mx-auto px-4 py-6 container">
    <!-- Header -->
    <div class="mb-6 flex flex-col items-start justify-between md:flex-row md:items-center">
      <h2 class="mb-4 text-xl font-medium md:mb-0">
        Talents
      </h2>

      <!-- Filters -->
      <div class="flex flex-wrap items-center gap-3">
        <div class="flex items-center text-sm">
          <span class="mr-2 text-gray-500">Status:</span>
          <button class="flex items-center border rounded bg-white px-3 py-1.5">
            {{ talents.filters.status }}
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="ml-2">
              <polyline points="6 9 12 15 18 9" />
            </svg>
          </button>
        </div>

        <div class="flex items-center text-sm">
          <span class="mr-2 text-gray-500">Sort:</span>
          <button class="flex items-center border rounded bg-white px-3 py-1.5">
            {{ talents.filters.sort }}
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="ml-2">
              <polyline points="6 9 12 15 18 9" />
            </svg>
          </button>
        </div>
      </div>
    </div>

    <!-- Talents grid -->
    <div class="grid grid-cols-1 gap-4 lg:grid-cols-3 md:grid-cols-2 xl:grid-cols-4">
      <TalentCard
        v-for="talent in talents.list"
        :key="talent.id"
        :talent="talent"
      />
    </div>
  </div>
</template>
