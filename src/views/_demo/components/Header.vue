<script>
export default {
  props: {
    user: {
      type: Object,
      required: true,
    },
    notifications: {
      type: Number,
      default: 0,
    },
  },
}
</script>

<template>
  <header class="fixed left-0 right-0 top-0 z-50 bg-[#1e2a3b] text-white shadow-md">
    <div class="mx-auto flex items-center justify-between px-4 py-3 container">
      <!-- Logo and navigation -->
      <div class="flex items-center space-x-6">
        <!-- Logo -->
        <div class="flex items-center">
          <div class="flex items-center text-xl text-[#409eff] font-bold">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-1">
              <circle cx="12" cy="12" r="10" />
              <circle cx="12" cy="12" r="4" />
            </svg>
            preline
          </div>
          <div class="relative ml-1">
            <span class="absolute h-2 w-2 flex -right-1 -top-1">
              <span class="absolute h-full w-full inline-flex animate-ping rounded-full bg-red-400 opacity-75" />
              <span class="relative h-2 w-2 inline-flex rounded-full bg-red-500" />
            </span>
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-gray-400">
              <polyline points="6 9 12 15 18 9" />
            </svg>
          </div>
        </div>

        <!-- Navigation -->
        <nav class="hidden items-center md:flex space-x-4">
          <a href="#" class="flex items-center rounded-md px-3 py-2 transition hover:bg-[#2a3a4d]">
            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-2">
              <path d="m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z" />
              <polyline points="9 22 9 12 15 12 15 22" />
            </svg>
            Home
          </a>
          <a href="#" class="flex items-center rounded-md px-3 py-2 transition hover:bg-[#2a3a4d]">
            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-2">
              <rect width="18" height="18" x="3" y="3" rx="2" />
              <path d="M3 9h18" />
              <path d="M9 21V9" />
            </svg>
            Projects
          </a>
          <a href="#" class="flex items-center rounded-md px-3 py-2 transition hover:bg-[#2a3a4d]">
            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-2">
              <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2" />
              <circle cx="9" cy="7" r="4" />
              <path d="M22 21v-2a4 4 0 0 0-3-3.87" />
              <path d="M16 3.13a4 4 0 0 1 0 7.75" />
            </svg>
            Talents
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="ml-1">
              <polyline points="6 9 12 15 18 9" />
            </svg>
          </a>
          <a href="#" class="flex items-center rounded-md px-3 py-2 transition hover:bg-[#2a3a4d]">
            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-2">
              <rect width="8" height="8" x="3" y="3" rx="2" />
              <path d="M7 11v4a2 2 0 0 0 2 2h4" />
              <rect width="8" height="8" x="13" y="13" rx="2" />
            </svg>
            Empty Contents
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="ml-1">
              <polyline points="6 9 12 15 18 9" />
            </svg>
          </a>
        </nav>
      </div>

      <!-- User section -->
      <div class="flex items-center space-x-4">
        <!-- Notifications -->
        <div class="relative">
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="cursor-pointer">
            <path d="M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9" />
            <path d="M10.3 21a1.94 1.94 0 0 0 3.4 0" />
          </svg>
          <span v-if="notifications" class="absolute h-4 w-4 flex items-center justify-center rounded-full bg-red-500 text-xs text-white -right-1 -top-1">
            {{ notifications }}
          </span>
        </div>

        <!-- User avatar -->
        <div class="flex cursor-pointer items-center">
          <img :src="user.avatar" alt="User avatar" class="size-8 rounded-full object-cover">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="ml-1 text-gray-400">
            <polyline points="6 9 12 15 18 9" />
          </svg>
        </div>
      </div>
    </div>
  </header>
</template>
