<script>
export default {
  name: 'ExpensesCard',
  props: {
    expenses: {
      type: Object,
      required: true,
    },
  },
  methods: {
    formatNumber(num) {
      return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
    },
    getBarHeight(value) {
      // Find the maximum value to scale the bars
      const maxValue = Math.max(...this.expenses.monthlyData.map(item => item.value))
      // Calculate height percentage (max height is 100%)
      return (value / maxValue) * 80 // 80% max height to leave room for labels
    },
  },
}
</script>

<template>
  <div class="overflow-hidden rounded-lg bg-white shadow-sm">
    <!-- Header -->
    <div class="flex items-center justify-between border-b p-5">
      <h2 class="text-lg font-medium">
        Expenses
      </h2>
      <div class="flex items-center text-sm text-gray-600">
        <svg
          xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
          stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-1"
        >
          <rect width="18" height="18" x="3" y="4" rx="2" ry="2" />
          <line x1="16" x2="16" y1="2" y2="6" />
          <line x1="8" x2="8" y1="2" y2="6" />
          <line x1="3" x2="21" y1="10" y2="10" />
        </svg>
        {{ expenses.dateRange }}
        <svg
          xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
          stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="ml-1"
        >
          <polyline points="6 9 12 15 18 9" />
        </svg>
      </div>
    </div>

    <!-- Expenses amount -->
    <div class="p-5 pb-0">
      <h3 class="text-2xl font-bold">
        ${{ formatNumber(expenses.total) }}
      </h3>
    </div>

    <!-- Chart -->
    <div class="h-64 p-5">
      <div class="h-full flex items-end">
        <div v-for="(item, index) in expenses.monthlyData" :key="index" class="flex flex-1 flex-col items-center">
          <div class="w-5/6 rounded-sm bg-[#409eff]" :style="`height: ${getBarHeight(item.value)}%`" />
          <span class="mt-2 text-xs text-gray-500">{{ item.month }}</span>
        </div>
      </div>
    </div>
  </div>
</template>
