<script>
export default {
  name: 'CompanyProfile',
  props: {
    company: {
      type: Object,
      required: true,
    },
  },
}
</script>

<template>
  <div class="bg-[#409eff] py-8 text-white">
    <div class="mx-auto px-4 container">
      <div class="flex flex-col items-center justify-between md:flex-row">
        <!-- Company info -->
        <div class="mb-4 flex items-center md:mb-0">
          <div class="mr-4 rounded-lg bg-yellow-300 p-2">
            <img :src="company.logo" alt="Company logo" class="h-12 w-12">
          </div>
          <div>
            <h1 class="text-2xl font-bold">
              {{ company.name }}
            </h1>
            <p class="text-sm text-white/80">
              {{ company.description }}
            </p>
          </div>
        </div>

        <!-- Invite button -->
        <button class="flex items-center rounded-md bg-white/10 px-4 py-2 text-white transition hover:bg-white/20">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-2">
            <path d="M5 12h14" />
            <path d="M12 5v14" />
          </svg>
          Invite talents
        </button>
      </div>
    </div>
  </div>
</template>
