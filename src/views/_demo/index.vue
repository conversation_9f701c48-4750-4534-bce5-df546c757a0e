<script>
export default {

  data() {
    return {
      user: {
        name: '<PERSON>',
        avatar: '/placeholder.svg?height=40&width=40',
      },
      notifications: 2,
      company: {
        name: '<PERSON>chimp',
        logo: '/placeholder.svg?height=80&width=80',
        description: 'Marketing, Automation & Email Platform',
      },
      profileSetup: {
        completed: 2,
        total: 4,
        requiredCompletion: 50,
        tasks: [
          { id: 1, title: 'Download desktop app', completed: true, action: 'Download' },
          { id: 2, title: 'Provide company details', completed: false, action: 'Add now' },
          { id: 3, title: 'Invite 5 talents', completed: true, action: 'Invite' },
          { id: 4, title: 'Add projects', completed: false, action: 'Add now' },
        ],
      },
      expenses: {
        total: 307000,
        dateRange: '25 Jul - 25 Aug',
        monthlyData: [
          { month: 'Jan', value: 20000 },
          { month: 'Feb', value: 30000 },
          { month: 'Mar', value: 28000 },
          { month: 'Apr', value: 35000 },
          { month: 'May', value: 15000 },
          { month: 'Jun', value: 32000 },
          { month: 'Jul', value: 28000 },
          { month: 'Aug', value: 15000 },
          { month: 'Sep', value: 20000 },
          { month: 'Oct', value: 30000 },
          { month: 'Nov', value: 18000 },
          { month: 'Dec', value: 32000 },
        ],
      },
      talents: {
        filters: {
          status: 'All',
          sort: 'Newest',
        },
        list: [
          {
            id: 1,
            name: 'Amanda Harvey',
            avatar: '/placeholder.svg?height=50&width=50',
            role: 'Front-End Developer',
            phone: '(*************',
            email: '<EMAIL>',
            isPro: true,
          },
          {
            id: 2,
            name: 'Daniel Hobbs',
            avatar: null,
            initials: 'D',
            role: 'Mobile Developer',
            phone: '******-00-00',
            email: '<EMAIL>',
            isPro: false,
          },
          {
            id: 3,
            name: 'Liza Harrison',
            avatar: null,
            initials: 'L',
            role: 'Illustrator',
            phone: '+33 000-00-00',
            email: '<EMAIL>',
            isPro: false,
          },
          {
            id: 4,
            name: 'Anna Richard',
            avatar: '/placeholder.svg?height=50&width=50',
            role: 'Marketing',
            email: '<EMAIL>',
            isPro: true,
          },
          {
            id: 5,
            name: 'Lewis Clarke',
            avatar: '/placeholder.svg?height=50&width=50',
            role: 'Full-Stack Developer',
            email: '<EMAIL>',
            isPro: true,
          },
          {
            id: 6,
            name: 'Ella Lauda',
            avatar: '/placeholder.svg?height=50&width=50',
            role: 'Brand Designer',
            phone: '******-00-11',
            email: '<EMAIL>',
            isPro: false,
          },
          {
            id: 7,
            name: 'Rachel Doe',
            avatar: '/placeholder.svg?height=50&width=50',
            role: 'Full-Stack Developer',
            phone: '+297 000-00-00',
            email: '<EMAIL>',
            isPro: false,
          },
          {
            id: 8,
            name: 'Ols Schols',
            avatar: null,
            initials: 'O',
            role: 'Web Designer',
            email: '<EMAIL>',
            isPro: true,
          },
        ],
      },
    }
  },
}
</script>

<template>
  <div class="min-h-screen bg-gray-100">
    <!-- Fixed header -->
    <Header :user="user" :notifications="notifications" />

    <!-- Main content -->
    <main class="pt-16">
      <!-- Add padding to account for fixed header -->
      <!-- Company profile section -->
      <CompanyProfile :company="company" />

      <!-- Dashboard cards section -->
      <div class="grid grid-cols-1 mx-auto gap-6 px-4 py-6 container md:grid-cols-2">
        <ProfileSetup :profile-data="profileSetup" />
        <ExpensesCard :expenses="expenses" />
      </div>

      <!-- Talents section -->
      <TalentsSection :talents="talents" />
    </main>
  </div>
</template>
