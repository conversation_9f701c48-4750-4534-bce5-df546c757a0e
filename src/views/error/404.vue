<script setup lang="ts">
const router = useRouter()

function goHome() {
  router.push({ name: 'Dashboard' })
}
</script>

<template>
  <div class="h-full min-h-150 flex flex-col rounded-lg bg-white px-5 py-6 shadow sm:px-6">
    <ElResult icon="error" title="404 - 页面未找到" sub-title="抱歉，您访问的页面不存在。">
      <template #extra>
        <ElButton type="primary" @click="goHome">
          返回首页
        </ElButton>
      </template>
    </ElResult>
  </div>
</template>
