<script setup lang="ts">
const router = useRouter()
const noticeStore = useNoticeStore()

const { notifications, totalNotifications } = storeToRefs(noticeStore)
const currentPage = ref(1)
const pageSize = ref(5)

// 获取消息列表数据
async function loadNoticesData() {
  await noticeStore.fetchNotices({
    page: currentPage.value,
    limit: pageSize.value,
  })
}

const filterState = reactive({
  currentReadStatus: 'all',
  currentNoticeType: 'all',
})

usePageConfig('noticePage', filterState, loadNoticesData)

// 监听分页变化，重新获取数据
watch([currentPage, pageSize], (_, oldVal) => {
  // 避免初始化时重复调用
  if (oldVal[0] !== undefined && oldVal[1] !== undefined) {
    loadNoticesData()
  }
})

// 筛选相关
const readStatusOptions = [
  { label: '全部', value: 'all' },
  { label: '已读', value: '1' },
  { label: '未读', value: '0' },
]

const noticeTypeOptions = [
  { label: '全部', value: 'all' },
  { label: '个人消息', value: '1' },
  { label: '系统公告', value: '2' },
]

// 搜索相关
const searchQuery = ref('')

// 根据筛选条件过滤通知
const filteredNotifications = computed(() => {
  return notifications.value.filter((item) => {
    // 按阅读状态筛选
    if (filterState.currentReadStatus !== 'all' && item.isRead !== filterState.currentReadStatus) {
      return false
    }
    // 按通知类型筛选
    if (filterState.currentNoticeType !== 'all' && item.noticeType !== filterState.currentNoticeType) {
      return false
    }
    // 按搜索关键词筛选（标题和内容）
    if (searchQuery.value.trim()) {
      const keyword = searchQuery.value.toLowerCase().trim()
      const titleMatch = item.noticeTitle.toLowerCase().includes(keyword)
      const contentMatch = item.noticeContent.toLowerCase().includes(keyword)
      if (!titleMatch && !contentMatch) {
        return false
      }
    }
    return true
  })
})

// 监听筛选条件变化，重置分页
watch([filterState, searchQuery], () => {
  currentPage.value = 1
})

// 处理分页变化
function handleCurrentChange(val: number) {
  currentPage.value = val
  // loadNoticesData() 不需要在这里调用，watch 会处理
}

// 处理通知点击，导航到详情页
function handleNoticeClick(item: NoticeItem) {
  // 标记为已读
  noticeStore.markNoticeAsRead(item.noticeId)

  // 导航到详情页
  // router.push({
  //   name: 'NoticeDetail',
  //   params: { id: item.noticeId },
  //   query: { page: currentPage.value.toString() },
  // })
}
</script>

<template>
  <div class="h-full flex flex-col rounded-lg bg-white px-5 py-6 shadow sm:px-6">
    <div class="border-b border-gray-200 pb-5">
      <h3 class="text-lg text-gray-800 font-medium leading-6">
        通知公告
      </h3>
      <!-- 补充功能栏 -->
      <div class="mt-4 flex items-center justify-between">
        <!-- 搜索框 -->
        <ElInput v-model.trim="searchQuery" style="width: 240px" placeholder="搜索通知..." />
        <!-- 筛选和排序 -->
        <div class="flex items-center space-x-4">
          <div class="flex items-center gap-2">
            <span class="w-12 text-sm text-gray-500">状态：</span>
            <ElSelect v-model="filterState.currentReadStatus" style="width: 100px">
              <ElOption
                v-for="option in readStatusOptions" :key="option.value" :label="option.label"
                :value="option.value"
              />
            </ElSelect>
          </div>
          <div class="flex items-center gap-2">
            <span class="w-12 text-sm text-gray-500">类型：</span>
            <ElSelect v-model="filterState.currentNoticeType" style="width: 100px">
              <ElOption
                v-for="option in noticeTypeOptions" :key="option.value" :label="option.label"
                :value="option.value"
              />
            </ElSelect>
          </div>
        </div>
      </div>
    </div>

    <!-- 通知列表 -->
    <div class="min-h-[514px] flex-grow overflow-y-auto divide-y divide-gray-200">
      <div
        v-for="item in filteredNotifications" :key="item.noticeId" class="relative px-6 py-4 text-sm transition-all" :class="[
          item.isRead === '0' ? 'bg-gray-50 hover:bg-gray-50' : 'bg-white hover:bg-gray-50',
          item.appFunction ? 'cursor-pointer' : 'cursor-default',
        ]" @click="noticeStore.handleNotificationClick(item)"
      >
        <div class="flex justify-between">
          <!-- 标题 -->
          <div class="flex items-center gap-2">
            <h3 class="text-base text-gray-800 font-medium">
              {{ item.noticeTitle }}
            </h3>
            <!-- 应用功能信息 -->
            <span v-if="item.appFunction" class="flex items-center gap-1 text-xs text-blue-600 font-medium">
              <i class="i-heroicons:link h-3.5 w-3.5" />
              {{ item.appFunction.name }} - {{ item.appFunction.appName }}
            </span>
          </div>
          <!-- 类型 -->
          <ElTag :type="item.noticeType === '1' ? 'success' : 'warning'" size="small" effect="light">
            {{ noticeStore.getNoticeTypeText(item.noticeType) }}
          </ElTag>
        </div>

        <!-- 内容 -->
        <p class="line-clamp-2 mt-1 text-xs text-gray-600 leading-normal">
          {{ item.noticeContent }}
        </p>

        <div class="mt-2 flex justify-between text-xs text-gray-400">
          <!-- 发布时间 -->
          <span>{{ item.createDate }}</span>
          <!-- 发布人 -->
          <span class="flex items-center gap-1">
            <i class="i-heroicons:megaphone h-4 w-4" />
            {{ item.sign }}
          </span>
        </div>

        <!-- 未读标记 -->
        <div v-if="item.isRead === '0'" class="absolute left-2 top-5.5 h-2 w-2 rounded-full bg-blue-500" />
      </div>

      <!-- 空状态 -->
      <div v-if="filteredNotifications.length === 0" class="flex flex-col items-center justify-center py-10 text-gray-400">
        <i class="i-heroicons:bell-slash mb-2 size-8" />
        <p class="text-sm">
          暂无通知公告
        </p>
      </div>
    </div>
    <!-- 分页 -->
    <div class="mt-6 flex justify-center">
      <ElPagination
        v-model:current-page="currentPage" :page-size="pageSize" :total="totalNotifications"
        layout="total, prev, pager, next" @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>
