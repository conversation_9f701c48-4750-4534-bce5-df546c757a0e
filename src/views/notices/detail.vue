<script setup lang="ts">
const router = useRouter()
const route = useRoute()
const noticeStore = useNoticeStore()
const appsStore = useAppsStore()

const noticeId = computed(() => Number(route.params.id))
const returnPage = computed(() => route.query.page ? Number(route.query.page) : 1)

// 当前通知数据
const notice = ref<NoticeItem | null>(null)
// 所有通知列表
const allNotices = computed(() => noticeStore.notifications)
// 当前通知在列表中的索引
const currentIndex = computed(() => {
  if (!notice.value)
    return -1
  return allNotices.value.findIndex(item => item.noticeId === notice.value?.noticeId)
})
// 是否有上一条通知
const hasPrevious = computed(() => currentIndex.value > 0)
// 是否有下一条通知
const hasNext = computed(() => currentIndex.value < allNotices.value.length - 1 && currentIndex.value !== -1)

// 获取通知详情
async function fetchNoticeDetail() {
  try {
    const data = await ApiGetNoticeDetail(noticeId.value)
    notice.value = data
  }
  catch (error) {
    ElMessage.error('获取通知详情失败')
    console.error('获取通知详情失败:', error)
  }
}

// 返回列表页
function goBackToList() {
  router.push({
    name: 'Notices',
    query: { page: returnPage.value.toString() },
  })
}

// 导航到上一条通知
function navigateToPrevious() {
  if (!hasPrevious.value)
    return

  const prevNotice = allNotices.value[currentIndex.value - 1]
  router.push({
    name: 'NoticeDetail',
    params: { id: prevNotice.noticeId },
    query: { page: returnPage.value.toString() },
  })
}

// 导航到下一条通知
function navigateToNext() {
  if (!hasNext.value)
    return

  const nextNotice = allNotices.value[currentIndex.value + 1]
  router.push({
    name: 'NoticeDetail',
    params: { id: nextNotice.noticeId },
    query: { page: returnPage.value.toString() },
  })
}

// 处理应用功能点击
async function handleAppFunctionClick() {
  if (notice.value?.appFunction) {
    await appsStore.handleAppOpening(notice.value.appFunction)
  }
}

// 页面加载时获取通知详情
onMounted(async () => {
  // 如果 store 中没有通知列表数据，则先获取列表数据
  if (allNotices.value.length === 0) {
    await noticeStore.fetchNotices({
      page: returnPage.value,
      limit: 5,
    })
  }

  await fetchNoticeDetail()
})

// 监听路由参数变化，重新获取通知详情
watch(() => route.params.id, () => {
  fetchNoticeDetail()
})
</script>

<template>
  <div class="h-full flex flex-col rounded-lg bg-white px-5 py-6 shadow sm:px-6">
    <!-- 顶部导航栏 -->
    <div class="mb-6 flex items-center justify-between border-b border-gray-200 pb-4">
      <div class="flex items-center gap-2">
        <!-- 返回按钮 -->
        <button
          class="flex items-center gap-1 text-sm text-blue-600 hover:text-blue-800"
          @click="goBackToList"
        >
          <i class="i-heroicons:arrow-left h-4 w-4" />
          返回列表
        </button>
      </div>

      <!-- 上一条/下一条导航 -->
      <div class="flex items-center gap-4">
        <button
          class="flex items-center gap-1 text-sm text-gray-600 disabled:cursor-not-allowed hover:text-gray-800 disabled:opacity-50"
          :disabled="!hasPrevious"
          @click="navigateToPrevious"
        >
          <i class="i-heroicons:chevron-left h-4 w-4" />
          上一条
        </button>
        <button
          class="flex items-center gap-1 text-sm text-gray-600 disabled:cursor-not-allowed hover:text-gray-800 disabled:opacity-50"
          :disabled="!hasNext"
          @click="navigateToNext"
        >
          下一条
          <i class="i-heroicons:chevron-right h-4 w-4" />
        </button>
      </div>
    </div>

    <!-- 通知详情内容 -->
    <div v-if="notice" class="flex-grow overflow-y-auto">
      <!-- 通知标题和类型 -->
      <div class="mb-6 flex items-start justify-between">
        <h1 class="text-2xl text-gray-800 font-medium">
          {{ notice.noticeTitle }}
        </h1>
        <ElTag :type="notice.noticeType === '1' ? 'success' : 'warning'" effect="light">
          {{ noticeStore.getNoticeTypeText(notice.noticeType) }}
        </ElTag>
      </div>

      <!-- 通知元信息 -->
      <div class="mb-6 flex items-center gap-6 text-sm text-gray-500">
        <div class="flex items-center gap-1">
          <i class="i-heroicons:calendar h-4 w-4" />
          <span>{{ notice.createDate }}</span>
        </div>
        <div class="flex items-center gap-1">
          <i class="i-heroicons:megaphone h-4 w-4" />
          <span>{{ notice.sign }}</span>
        </div>
        <div class="flex items-center gap-1">
          <i class="i-heroicons:eye h-4 w-4" />
          <span>阅读数：{{ notice.readNum }}</span>
        </div>
      </div>

      <!-- 通知内容 -->
      <div class="border-t border-gray-100 pt-6">
        <div class="max-w-none text-gray-700 prose">
          {{ notice.noticeContent }}
        </div>
      </div>

      <!-- 应用功能链接 -->
      <div v-if="notice.appFunction" class="mt-8 flex items-center justify-center">
        <button
          class="flex items-center gap-2 rounded-md bg-blue-600 px-4 py-2 text-sm text-white hover:bg-blue-700"
          @click="handleAppFunctionClick"
        >
          <i class="i-heroicons:link h-4 w-4" />
          打开 {{ notice.appFunction.name }} - {{ notice.appFunction.appName }}
        </button>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-else class="flex flex-grow items-center justify-center">
      <ElSkeleton :rows="10" animated />
    </div>
  </div>
</template>
