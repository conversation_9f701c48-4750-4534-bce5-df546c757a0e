<script setup lang="ts">
const appsStore = useAppsStore()
const getTailwindColorByName = appsStore.getTailwindColorByName

const RecentList = ref<FunctionItem[]>([])

// 控制加载状态 (骨架屏和刷新动画)
const isLoading = ref(true)

/**
 * 处理刷新按钮点击
 */
function handleRefreshClick() {
  if (isLoading.value)
    return
  loadRecentVisitsData()
}

/**
 * 获取最近访问功能列表数据
 */
async function loadRecentVisitsData() {
  isLoading.value = true
  try {
    const { list } = await appsStore.fetchFunctions({
      page: 1,
      limit: 8, // 获取前 8 个
      sort: 'click_last_date:desc', // 按最后点击时间降序排序
    })
    RecentList.value = list
  }
  finally {
    isLoading.value = false
  }
}

onMounted(() => {
  loadRecentVisitsData()
})
</script>

<template>
  <ModuleCard title="最近访问">
    <!-- 更多操作插槽 -->
    <template #more>
      <button
        class="flex items-center justify-center rounded-full p-1.5 text-gray-400 transition-colors hover:bg-gray-100 hover:text-gray-600"
        title="刷新最近访问" @click="handleRefreshClick"
      >
        <i class="i-heroicons:arrow-path size-4" :class="{ 'animate-spin': isLoading }" />
      </button>
    </template>

    <ElSkeleton :loading="isLoading">
      <template #template>
        <div class="grid grid-cols-2 gap-px overflow-hidden bg-gray-100 xl:grid-cols-4">
          <div v-for="i in 8" :key="i" class="flex items-center justify-between bg-white p-4">
            <div class="flex items-center">
              <div class="size-8 rounded-full bg-gray-100" />
              <div class="ml-2 h-4 w-20 rounded bg-gray-100" />
            </div>
            <div class="h-4 w-4 rounded bg-gray-100" />
          </div>
        </div>
      </template>
      <template #default>
        <div
          class="grid grid-cols-2 gap-px overflow-hidden bg-gray-100 text-gray-700 xl:grid-cols-4 divide-y divide-y-0 divide-gray-200"
        >
          <div
            v-for="(item) in RecentList" :key="item.appName"
            class="group relative flex cursor-pointer items-center justify-between bg-white p-4 transition duration-300 ease-in-out hover:bg-gray-50 focus-within:ring-2 focus-within:ring-blue-500 focus-within:ring-inset"
            @click="appsStore.handleAppOpening(item)"
          >
            <div class="flex items-center">
              <div>
                <span
                  class="inline-flex rounded-full p-2 ring-1 ring-gray-50/50 transition-all duration-400 group-hover:ring-white"
                  :class="getTailwindColorByName(item.color)"
                >
                  <Icon
                    :icon="item.icon" class="size-4 transition-all duration-400 group-hover:text-white"
                    aria-hidden="true"
                  />
                </span>
              </div>
              <p class="ml-2 text-sm">
                {{ item.name }}
              </p>
            </div>
            <div class="text-right">
              <small v-if="item.clickLastDate" class="text-xs text-gray-300">
                {{ useDate.fromNow(item.clickLastDate) }}
              </small>
              <i
                class="i-heroicons:arrow-up-right-solid size-4 text-gray-300 transition-all duration-400 group-hover:text-gray-500"
              />
            </div>
          </div>
          <div v-if="RecentList.length === 0" class="col-span-2 bg-white py-8 text-center text-gray-300 xl:col-span-4">
            <i class="i-heroicons:cursor-arrow-ripple mb-2 size-8" />
            <p class="text-sm">
              暂无最近访问
            </p>
          </div>
        </div>
      </template>
    </ElSkeleton>
  </ModuleCard>
</template>
