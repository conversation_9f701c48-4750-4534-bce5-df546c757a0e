<script setup lang="ts">
import { Disclosure, DisclosureButton, DisclosurePanel } from '@headlessui/vue'

const faqs = ref<FaqItem[]>([])

async function loadFaqsData() {
  const { list } = await ApiGetFaqs({ page: 1, limit: 3 })
  faqs.value = list
}

onMounted(() => {
  loadFaqsData()
})
</script>

<template>
  <ModuleCard title="常见问题">
    <div class="px-6 pb-6 pt-2">
      <div class="mx-auto max-w-4xl divide-y divide-gray-900/10">
        <div v-if="faqs.length === 0" class="flex flex-col items-center justify-center py-8 text-gray-300">
          <i class="i-heroicons:question-mark-circle mb-2 size-8" />
          <p class="text-sm">
            暂无常见问题
          </p>
        </div>

        <!-- FAQ 列表 -->
        <dl v-else class="space-y-4 divide-y divide-gray-900/10">
          <Disclosure v-for="faq in faqs" :key="faq.id" v-slot="{ open }" as="div" class="pt-4">
            <dt>
              <DisclosureButton class="w-full flex items-start justify-between text-left text-gray-800">
                <span class="text-base font-semibold leading-6">{{ faq.question }}</span>
                <span class="ml-6 h-7 flex items-center">
                  <i v-if="!open" class="i-heroicons:plus h-6 w-6" aria-hidden="true" />
                  <i v-else class="i-heroicons:minus h-6 w-6" aria-hidden="true" />
                </span>
              </DisclosureButton>
            </dt>
            <Transition
              enter-active-class="transition-all duration-400 ease-in-out overflow-hidden"
              enter-from-class="max-h-0 opacity-0" enter-to-class="max-h-[500px] opacity-100"
              leave-active-class="transition-all duration-200 ease-in-out overflow-hidden"
              leave-from-class="max-h-[500px] opacity-100" leave-to-class="max-h-0 opacity-0"
            >
              <DisclosurePanel as="dd" class="mt-2 pr-12">
                <p class="text-sm text-gray-600 leading-6">
                  {{ faq.answer }}
                </p>
              </DisclosurePanel>
            </Transition>
          </Disclosure>
        </dl>
      </div>
    </div>
  </ModuleCard>
</template>
