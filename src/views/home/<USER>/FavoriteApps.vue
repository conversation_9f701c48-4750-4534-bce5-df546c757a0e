<script setup lang="ts">
const appList = ref<AppItem[]>([])

const appsStore = useAppsStore()
const getTailwindColorByName = appsStore.getTailwindColorByName

const componentState = reactive({
  isExpanded: false,
})

// 显示的应用列表（根据展开状态决定）
const displayedApps = computed(() => {
  if (componentState.isExpanded || appList.value.length <= 3) {
    return appList.value
  }
  return appList.value.slice(0, 3)
})

// 是否显示展开/收起按钮
const showToggleButton = computed(() => appList.value.length > 3)

// 获取收藏应用列表数据
async function loadFavoriteAppsData() {
  const { list } = await appsStore.fetchApps({
    page: 1,
    limit: 100,
    sort: 'bind_user:desc', // 未绑定的排在后面
  })
  appList.value = list.filter(item => item.isTop === '1') // 只显示收藏的应用
}

// 处理应用点击
async function handleAppClick(item: AppItem) {
  await appsStore.handleAppOpening(item)
}

// 切换展开/收起状态
function toggleExpanded() {
  componentState.isExpanded = !componentState.isExpanded
}

usePageConfig('homeFavoriteApps', componentState, loadFavoriteAppsData)
</script>

<template>
  <ModuleCard title="收藏应用">
    <!-- 更多操作插槽 -->
    <template #more>
      <RouterLink to="/apps">
        <span class="text-sm text-gray-500 hover:text-gray-700">全部应用</span>
      </RouterLink>
    </template>

    <!-- 应用列表 -->
    <TransitionGroup
      tag="div"
      name="list"
      class="divide-y divide-gray-100"
    >
      <div
        v-for="item in displayedApps"
        :key="item.appId"
        class="group flex cursor-pointer items-center gap-2 bg-white p-4 transition-all duration-300 ease-in-out hover:bg-gray-50"
        @click="handleAppClick(item)"
      >
        <!-- 应用图标 -->
        <div
          :class="getTailwindColorByName(item.color)"
          class="size-10 flex flex-shrink-0 items-center justify-center rounded-md transition-all duration-400"
        >
          <Icon
            :icon="item.icon" class="size-6 transition-all duration-400 group-hover:text-white"
            aria-hidden="true"
          />
        </div>

        <!-- 应用信息 -->
        <div class="min-w-0 flex flex-1 items-center justify-between">
          <h4 class="truncate text-sm text-gray-800 font-medium leading-tight">
            {{ item.appName }}
          </h4>

          <!-- 绑定状态 -->
          <div class="ml-4 flex-shrink-0">
            <div
              v-if="item.bindUser"
              class="inline-flex items-center gap-1 rounded-full bg-green-50 px-2.5 py-0.5 text-xs text-green-700 font-medium"
            >
              <i class="i-heroicons:user size-3" />
              <span>{{ item.bindUser }}</span>
            </div>
            <div
              v-else
              class="inline-flex items-center gap-1 rounded-full bg-orange-50 px-2.5 py-0.5 text-xs text-orange-600 font-medium"
            >
              <i
                class="size-3"
                :class="item.bindType === '1' ? 'i-heroicons:lock-closed' : 'i-heroicons:device-phone-mobile'"
              />
              <span>待绑定</span>
            </div>
          </div>
        </div>

        <!-- 箭头指示器 -->
        <div class="flex-shrink-0">
          <i
            class="i-heroicons:chevron-right size-6 text-gray-300 transition-all duration-300 group-hover:translate-x-1 group-hover:text-gray-500"
          />
        </div>
      </div>
    </TransitionGroup>

    <!-- 展开/折叠按钮 -->
    <div
      v-if="showToggleButton"
      class="group w-full flex flex cursor-pointer items-center justify-center gap-2 p-4 text-sm text-gray-500 transition-all duration-200 hover:text-gray-700"
      @click="toggleExpanded"
    >
      <span>{{ componentState.isExpanded ? '折叠' : `展开全部 (${appList.length})` }}</span>
      <i
        class="size-4 transition-transform duration-200"
        :class="componentState.isExpanded ? 'i-heroicons:chevron-up rotate-0' : 'i-heroicons:chevron-down'"
      />
    </div>

    <!-- 空状态 -->
    <div v-if="appList.length === 0" class="flex flex-col items-center justify-center py-8 text-gray-300">
      <i class="i-heroicons:star mb-2 size-8" />
      <p class="text-sm">
        暂无收藏应用
      </p>
    </div>
  </ModuleCard>
</template>

<style scoped>
.list-move,
.list-enter-active,
.list-leave-active {
  transition: all 0.5s ease;
}

.list-enter-from,
.list-leave-to {
  opacity: 0;
  transform: translateY(30px);
}

.list-leave-active {
  position: absolute;
}
</style>
