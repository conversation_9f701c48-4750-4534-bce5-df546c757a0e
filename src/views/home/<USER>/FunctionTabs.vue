<script setup lang="ts">
const searchKeyword = ref<string>('')
const appsStore = useAppsStore()

interface TabItem {
  type: string
  name: string
  icon: any
}

const tabs: TabItem[] = [
  { type: '1', name: '教育管理', icon: 'i-heroicons:academic-cap-solid' },
  { type: '2', name: '学生成长', icon: 'i-heroicons:user-group-solid' },
  { type: '3', name: '教师成长', icon: 'i-heroicons:users-solid' },
  { type: '4', name: '学校发展', icon: 'i-heroicons:building-library-solid' },
  { type: '5', name: '教学教研', icon: 'i-heroicons:book-open-solid' },
  { type: '6', name: '数据中心', icon: 'i-heroicons:chart-bar-solid' },
]

/**
 * 获取功能列表数据
 */
async function loadFunctionsData() {
  if (appsStore.functionList.length > 0) {
    return
  }
  const { list } = await appsStore.fetchFunctions({
    page: 1,
    limit: 100,
    // sort: 'name:asc', // 不传默认按 name 升序
  })
  appsStore.functionList = list
}

/**
 * 处理 Tab 点击事件
 * @param type - 被点击的 Tab 的类型
 */
const componentState = reactive({
  currentTabType: '1',
  showBoundOnly: false,
})

function handleTabClick(type: string) {
  componentState.currentTabType = type
}

// 根据当前选中的 Tab 类型和搜索关键词筛选功能列表
const filteredFunctionList = computed(() => {
  // 确保 functionList 是数组
  if (!Array.isArray(appsStore.functionList)) {
    return []
  }

  // 1. 根据“只显示已绑定”开关进行预过滤
  const preFilteredList = componentState.showBoundOnly
    ? appsStore.functionList.filter(item => item.isBind === '1')
    : appsStore.functionList

  // 2. 按 Tab 类型筛选
  const filteredByType = preFilteredList.filter(item => item.type === componentState.currentTabType)

  // 3. 如果没有搜索关键词，直接返回
  if (!searchKeyword.value) {
    return filteredByType
  }

  // 4. 按搜索关键词进一步筛选
  return filteredByType.filter(item =>
    item.name.toLowerCase().includes(searchKeyword.value.toLowerCase()),
  )
})

// 预先计算所有 Tab 的功能数量，以优化性能
const tabCounts = computed(() => {
  const counts = new Map<string, number>()
  if (!Array.isArray(appsStore.functionList)) {
    return counts
  }

  // 初始化所有 tab 的计数为 0
  for (const tab of tabs) {
    counts.set(tab.type, 0)
  }

  // 1. 根据“只显示已绑定”进行过滤
  const boundFilteredList = componentState.showBoundOnly
    ? appsStore.functionList.filter(item => item.isBind === '1')
    : appsStore.functionList

  // 2. 根据搜索关键词进一步过滤
  const listToCount = searchKeyword.value
    ? boundFilteredList.filter(item =>
        item.name.toLowerCase().includes(searchKeyword.value.toLowerCase()),
      )
    : boundFilteredList

  // 遍历最终列表，累加每个类型的数量
  for (const item of listToCount) {
    if (counts.has(item.type)) {
      counts.set(item.type, (counts.get(item.type) || 0) + 1)
    }
  }

  return counts
})

// 检测屏幕尺寸，决定是否显示 tooltip
const { width } = useWindowSize()
const shouldShowTooltip = computed(() => width.value < 1280) // xl 断点是 1280px

usePageConfig('homeFunctionTabs', componentState, loadFunctionsData)
</script>

<template>
  <ModuleCard title="功能中心" class="h-full">
    <!-- 更多操作插槽 -->
    <template #more>
      <div class="flex items-center gap-x-4">
        <ElSwitch v-model="componentState.showBoundOnly" size="small" active-text="筛选已绑定" />
        <div class="relative">
          <input
            v-model="searchKeyword" type="text" placeholder="搜索功能"
            class="border border-gray-300 rounded-md py-1 pl-8 pr-8 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
          >
          <i
            class="i-heroicons:magnifying-glass absolute left-2 top-1/2 size-4 text-gray-400 -translate-y-1/2"
            aria-hidden="true"
          />
          <!-- 清空按钮 -->
          <button
            v-if="searchKeyword"
            class="absolute right-2 top-1/2 text-gray-400 -translate-y-1/2 hover:text-gray-600"
            @click="searchKeyword = ''"
          >
            <i class="i-heroicons:x-circle size-3.5" aria-hidden="true" />
          </button>
        </div>
      </div>
    </template>

    <div class="flex flex-grow flex-col">
      <div class="border-b border-gray-200 px-4">
        <nav class="flex gap-x-4 overflow-x-auto scrollbar-hide -mb-px" aria-label="Tabs">
          <ElTooltip
            v-for="tab in tabs" :key="tab.type" :content="tab.name" placement="top"
            :disabled="!shouldShowTooltip" effect="light"
          >
            <a
              class="group inline-flex flex-shrink-0 cursor-pointer items-center gap-x-1.5 whitespace-nowrap border-b-2 px-1 py-4 text-sm font-medium"
              :class="[componentState.currentTabType === tab.type ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700']"
              :aria-current="componentState.currentTabType === tab.type ? 'page' : undefined"
              @click="handleTabClick(tab.type)"
            >
              <i
                class="h-5 w-5 -ml-0.5"
                :class="[componentState.currentTabType === tab.type ? 'text-blue-500' : 'text-gray-400 group-hover:text-gray-500', tab.icon]"
                aria-hidden="true"
              />
              <span class="hidden whitespace-nowrap xl:inline-block">{{ tab.name }}</span>
              <small
                class="min-h-5 min-w-5 inline-flex items-center justify-center rounded-full px-1.5 py-0.5 text-xs font-medium"
                :class="[
                  componentState.currentTabType === tab.type
                    ? 'bg-blue-100 text-blue-600'
                    : 'bg-gray-100 text-gray-500 group-hover:bg-gray-200',
                ]"
              >
                {{ tabCounts.get(tab.type) || 0 }}
              </small>
            </a>
          </ElTooltip>
        </nav>
      </div>

      <div class="flex flex-grow flex-col px-4 py-4">
        <div v-if="filteredFunctionList.length > 0" class="grid grid-cols-1 gap-4 md:grid-cols-2 xl:grid-cols-3">
          <div
            v-for="(item, index) in filteredFunctionList" :key="item.id"
            class="group flex cursor-pointer items-center justify-between overflow-hidden border border-gray-100 rounded-lg bg-white p-4 transition duration-300 ease-in-out hover:border-gray-200 hover:bg-gray-50"
            @click="appsStore.handleAppOpening(item)"
          >
            <div class="group flex items-center gap-3">
              <div
                :class="appsStore.getTailwindColorByName(item.color)"
                class="size-10 flex flex-shrink-0 items-center justify-center rounded-full transition-all duration-400"
              >
                <Icon
                  :icon="item.icon" class="size-6 transition-all duration-400 group-hover:text-white"
                  aria-hidden="true"
                />
              </div>
              <div class="min-w-0 flex-1">
                <p class="truncate text-sm text-gray-800 font-medium">
                  {{ item.name }}
                </p>
                <p class="truncate text-xs text-gray-500">
                  {{ item.appName }}
                </p>
              </div>
            </div>
            <div class="ml-4 flex-shrink-0 italic">
              <!-- 统计信息 -->
              <div v-if="item.statValue !== null" class="flex items-baseline gap-x-1 text-right text-xs text-gray-500">
                <span>{{ item.statName || '学生总数' }}</span>
                <span class="text-lg text-blue-500">
                  {{ item.statValue || '10' }}
                </span>
                <span>{{ item.statUnit || '万人' }}</span>
              </div>
              <!-- 序号 -->
              <div v-else class="text-sm text-gray-400">
                #{{ index + 1 }}
              </div>
            </div>
          </div>
        </div>
        <div v-else class="h-full flex flex-col items-center justify-center pt-15 text-gray-300">
          <i class="i-heroicons:inbox mb-2 size-8" aria-hidden="true" />
          <span class="text-sm">该分类下暂无功能</span>
        </div>
      </div>
    </div>
  </ModuleCard>
</template>
