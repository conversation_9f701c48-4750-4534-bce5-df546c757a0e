<script setup lang="ts">
const appsStore = useAppsStore()
</script>

<template>
  <div class="grid grid-cols-1 mx-auto gap-6 pb-6 md:grid-cols-12">
    <div class="flex flex-col gap-6 md:col-span-7 xl:col-span-8">
      <RecentVisits />
      <div class="flex-1">
        <FunctionTabs class="h-full" />
      </div>
    </div>

    <div class="flex flex-col gap-6 md:col-span-5 xl:col-span-4">
      <FavoriteApps />
      <Notices />
      <FAQs />
    </div>
  </div>

  <!-- 浮动二维码 -->
  <FloatingQRCode />
  <!-- 绑定应用对话框组件 -->
  <BindAppDialog v-model:visible="appsStore.bindDialogVisible" :app="appsStore.currentApp" />
</template>
