<script setup lang="ts">
const noticeStore = useNoticeStore()
const { notifications, totalNotifications } = storeToRefs(noticeStore)

// 获取应用列表数据
async function loadNoticesData() {
  await noticeStore.fetchNotices({ page: 1, limit: 3 })
}

onMounted(() => {
  loadNoticesData()
})
</script>

<template>
  <ModuleCard title="最新通知">
    <!-- 消息列表 -->
    <div class="divide-y">
      <div
        v-for="item in notifications" :key="item.noticeId" class="relative cursor-pointer px-6 py-4 text-sm transition-all" :class="[
          item.isRead === '0' ? 'bg-gray-50 hover:bg-gray-50' : 'bg-white hover:bg-gray-50',
          item.appFunction ? 'cursor-pointer' : 'cursor-default',
        ]" @click="noticeStore.handleNotificationClick(item)"
      >
        <div class="flex justify-between">
          <!-- 标题 -->
          <h3 class="text-base text-gray-800 font-medium">
            {{ item.noticeTitle }}
          </h3>
          <!-- 类型 -->
          <ElTag :type="item.noticeType === '1' ? 'success' : 'warning'" size="small" effect="light">
            {{ noticeStore.getNoticeTypeText(item.noticeType) }}
          </ElTag>
        </div>

        <!-- 应用功能信息 -->
        <p v-if="item.appFunction" class="mt-1 flex items-center gap-1 text-xs text-blue-600 font-medium">
          <i class="i-heroicons:link h-3.5 w-3.5" />
          {{ item.appFunction.name }} - {{ item.appFunction.appName }}
        </p>

        <!-- 内容 -->
        <p class="line-clamp-2 mt-1 text-xs text-gray-600 leading-normal">
          {{ item.noticeContent }}
        </p>

        <div class="mt-2 flex justify-between text-xs text-gray-400">
          <!-- 发布时间 -->
          <span>{{ item.createDate }}</span>
          <!-- 发布人 -->
          <span class="flex items-center gap-1">
            <i class="i-heroicons:megaphone h-4 w-4" />
            {{ item.sign }}
          </span>
        </div>

        <!-- 未读标记 -->
        <div v-if="item.isRead === '0'" class="absolute left-2 top-5.5 h-2 w-2 rounded-full bg-blue-500" />
      </div>

      <!-- 空状态 -->
      <div v-if="notifications.length === 0" class="flex flex-col items-center justify-center py-8 text-gray-300">
        <i class="i-heroicons:chat-bubble-left-right mb-2 size-8" />
        <p class="text-sm">
          暂无消息
        </p>
      </div>
    </div>

    <!-- 底部操作区域 -->
    <template #footer>
      <div class="flex items-center justify-center text-sm">
        <RouterLink to="/notices">
          查看全部 ({{ totalNotifications }})
        </RouterLink>
      </div>
    </template>
  </ModuleCard>
</template>
