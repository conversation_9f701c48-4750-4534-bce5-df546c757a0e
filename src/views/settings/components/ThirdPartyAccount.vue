<script setup lang="ts">
import { ElLoading, ElMessage, ElMessageBox } from 'element-plus'

// 直接从 store 获取用户信息
const userStore = useUserStore()
const userInfo = computed(() => userStore.userInfo)

/**
 * 初始化第三方账号列表
 */
const socialAccounts = ref<Array<AuthItem & { name: string, icon: string, bound: boolean }>>([
  { source: 'wechat_open', name: '微信', icon: 'i-ri:wechat-fill', color: 'text-hex-07C160', bound: false, authId: '', userName: '', avatar: '', createDate: '' },
  { source: 'dingtalk', name: '钉钉', icon: 'i-ri:dingding-fill', color: 'text-hex-2E88FF', bound: false, authId: '', userName: '', avatar: '', createDate: '' },
])

/**
 * 初始化第三方账号绑定状态
 */
function initSocialAccounts() {
  // 首先重置所有账号为未绑定状态
  socialAccounts.value.forEach((account) => {
    account.bound = false
    account.userName = ''
    account.avatar = ''
    account.createDate = ''
    account.authId = ''
  })

  // 如果用户信息中有 auths 属性，则更新绑定状态
  const auths = userInfo.value?.auths || []
  auths.forEach((auth: AuthItem) => {
    const account = socialAccounts.value.find(a => a.source === auth.source)
    if (account) {
      account.bound = true
      account.userName = auth.userName || ''
      account.avatar = auth.avatar || ''
      account.createDate = auth.createDate || ''
      account.authId = auth.authId || ''
    }
  })
}

// 初始化
onMounted(() => {
  initSocialAccounts()
})

// 监听 userInfo 变化，重新初始化第三方账号绑定状态
watch(userInfo, () => {
  initSocialAccounts()
}, { deep: true })

/**
 * 绑定第三方账号
 * @param source - 第三方平台标识 (如 'wechat_open', 'dingtalk')
 */
async function bindSocial(source: ThirdPartySource) {
  try {
    const loadingInstance = ElLoading.service({
      lock: true,
      text: '正在获取授权链接...',
    })

    try {
      // 调用 API 获取绑定链接
      const { url } = await ApiGetThirdUrl(source)
      if (url) {
        // 跳转到第三方授权页面
        window.location.href = url
      }
      else {
        ElMessage.error('获取绑定链接失败，返回的 URL 为空')
      }
    }
    finally {
      loadingInstance.close()
    }
  }
  catch (error) {
    console.error('获取绑定链接失败：', error)
    ElMessage.error('获取绑定链接失败，请稍后重试')
  }
}

/**
 * 解绑第三方账号
 * @param account - 账号信息
 */
async function unbindSocial(account: AuthItem & { name: string, icon: string, bound: boolean }) {
  if (!account.authId) {
    ElMessage.error('授权 ID 不存在，无法解绑')
    return
  }

  try {
    await ElMessageBox.confirm(`确定要解除 ${account.name} 绑定吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })

    const loadingInstance = ElLoading.service({
      lock: true,
      text: '正在解除绑定...',
    })

    try {
      // 调用解绑 API
      await ApiUnbindThird(account.authId)

      ElMessage.success(`已成功解除 ${account.name} 绑定`)

      // 重新获取用户信息
      await userStore.fetchUserInfo()

      // 更新本地绑定状态
      initSocialAccounts()
    }
    finally {
      loadingInstance.close()
    }
  }
  catch (error: any) {
    if (error !== 'cancel') {
      console.error('解绑失败：', error)
      ElMessage.error(error?.message || '解绑失败，请稍后重试')
    }
  }
}
</script>

<template>
  <div class="space-y-4">
    <h2>
      <i class="i-mingcute:link-3-line mr-2 text-blue-500" />
      第三方账号
    </h2>
    <p class="mb-4 text-sm text-gray-500">
      绑定第三方账号后，可以直接使用第三方账号扫码登录系统
    </p>

    <div class="overflow-hidden border border-gray-200 rounded-md">
      <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
          <tr>
            <th class="w-1/6 px-4 py-3 text-left text-xs text-gray-500 font-medium tracking-wider uppercase">
              平台
            </th>
            <th class="w-1/6 px-4 py-3 text-left text-xs text-gray-500 font-medium tracking-wider uppercase">
              状态
            </th>
            <th class="w-2/6 px-4 py-3 text-left text-xs text-gray-500 font-medium tracking-wider uppercase">
              账号信息
            </th>
            <th class="w-1/6 px-4 py-3 text-left text-xs text-gray-500 font-medium tracking-wider uppercase">
              绑定时间
            </th>
            <th class="w-1/6 px-4 py-3 text-right text-xs text-gray-500 font-medium tracking-wider uppercase">
              操作
            </th>
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
          <tr v-for="account in socialAccounts" :key="account.source">
            <!-- 平台 -->
            <td class="whitespace-nowrap px-4 py-3">
              <div class="flex items-center">
                <div class="h-10 w-10 flex flex-shrink-0 items-center justify-center">
                  <i :class="`${account.icon} ${account.color}`" class="text-2xl" />
                </div>
                <div class="ml-3">
                  <div class="text-sm text-gray-800 font-medium">
                    {{ account.name }}
                  </div>
                </div>
              </div>
            </td>

            <!-- 状态 -->
            <td class="whitespace-nowrap px-4 py-3">
              <span
                v-if="account.bound"
                class="inline-flex items-center rounded-full bg-green-100 px-2.5 py-0.5 text-xs text-green-800 font-medium"
              >
                已绑定
              </span>
              <span
                v-else
                class="inline-flex items-center rounded-full bg-gray-100 px-2.5 py-0.5 text-xs text-gray-800 font-medium"
              >
                未绑定
              </span>
            </td>

            <!-- 账号信息 -->
            <td class="whitespace-nowrap px-4 py-3">
              <div v-if="account.userName" class="flex items-center">
                <ElAvatar v-if="account.avatar" :src="account.avatar" :size="32" class="mr-3" shape="square" />
                <ElAvatar v-else :size="32" class="mr-3" shape="square">
                  <i class="i-heroicons:user-solid" />
                </ElAvatar>
                <div class="text-sm text-gray-800">
                  {{ account.userName || '' }}
                </div>
              </div>
            </td>

            <!-- 绑定时间 -->
            <td class="whitespace-nowrap px-4 py-3 text-sm text-gray-500">
              {{ account.createDate || '' }}
            </td>

            <!-- 操作 -->
            <td class="whitespace-nowrap px-4 py-3 text-right">
              <ElButton v-if="account.bound" type="danger" plain size="small" @click="unbindSocial(account)">
                解除绑定
              </ElButton>
              <ElButton v-else type="primary" plain size="small" @click="bindSocial(account.source)">
                立即绑定
              </ElButton>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>
