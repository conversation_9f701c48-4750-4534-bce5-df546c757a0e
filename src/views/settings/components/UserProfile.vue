<script setup lang="ts">
const userStore = useUserStore()
const userInfo = computed(() => userStore.userInfo)

function formatSex(sex: string | undefined): string {
  const sexMap: Record<string, string> = {
    1: '男',
    0: '女',
  }
  return sexMap[sex as string] || '保密'
}

// 用户信息配置
const userInfoItems = computed(() => [
  {
    label: '用户名',
    value: userInfo.value?.userName || 'N/A',
    icon: 'i-heroicons:user',
    color: 'text-blue-500',
  },
  {
    label: '手机号码',
    value: userInfo.value?.phoneNumber || 'N/A',
    icon: 'i-heroicons:phone',
    color: 'text-green-500',
  },
  {
    label: '邮箱',
    value: userInfo.value?.email || 'N/A',
    icon: 'i-heroicons:envelope',
    color: 'text-purple-500',
  },
  {
    label: '性别',
    value: formatSex(userInfo.value?.sex),
    icon: 'i-heroicons:identification',
    color: 'text-pink-500',
  },
  {
    label: '部门',
    value: userInfo.value?.dept || 'N/A',
    icon: 'i-heroicons:building-office',
    color: 'text-orange-500',
  },
  {
    label: '最后登录',
    value: userInfo.value?.lastLoginDate || 'N/A',
    icon: 'i-heroicons:clock',
    color: 'text-gray-500',
  },
])
</script>

<template>
  <!-- 左右布局容器 -->
  <div class="flex flex-col gap-8 md:flex-row">
    <!-- 左侧：用户头像和基本信息 -->
    <div class="md:w-1/4">
      <div class="flex flex-col items-center">
        <img :src="userInfo?.avatar" alt="用户头像" class="size-28 rounded-full object-cover ring-4 ring-gray-100">
        <h2 class="mt-4 text-center text-xl text-gray-800 font-semibold">
          {{ userInfo?.nickName || 'N/A' }}
        </h2>
        <p class="mt-1 text-center text-sm text-gray-500">
          用户 ID: {{ userInfo?.userId || 'N/A' }}
        </p>
        <div class="mt-4 text-center text-xs text-gray-400">
          <i class="i-heroicons:information-circle" />
          修改用户信息请在数枢平台进行修改
        </div>
      </div>
    </div>

    <!-- 右侧：用户详细信息 -->
    <div class="md:w-3/4 md:border-l md:border-gray-200 md:pl-8">
      <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
        <div v-for="item in userInfoItems" :key="item.label" class="flex items-center gap-3 py-2">
          <div class="size-11 flex items-center justify-center rounded-lg bg-gray-50">
            <i :class="[item.icon, item.color]" class="size-6" />
          </div>
          <div class="flex-1">
            <span class="text-sm text-gray-600">{{ item.label }}</span>
            <p class="text-sm text-gray-800 font-medium">
              {{ item.value }}
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
