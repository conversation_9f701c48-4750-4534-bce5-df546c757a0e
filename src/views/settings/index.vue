<script setup lang="ts">
const userStore = useUserStore()

async function clearCache() {
  localStorage.clear()
  ElMessage({
    type: 'success',
    message: '配置缓存已成功清除',
  })
}

onMounted(async () => {
  await userStore.fetchUserInfo()
})
</script>

<template>
  <div class="h-full flex flex-col rounded-lg bg-white px-5 py-6 shadow sm:px-6">
    <div class="border-b border-gray-200 pb-5">
      <div class="flex items-center justify-between">
        <div>
          <h3 class="text-lg text-gray-800 font-medium leading-6">
            个人中心
          </h3>
          <p class="mt-1 text-sm text-gray-500">
            查看个人信息和第三方账号绑定
          </p>
        </div>
        <ElButton class="w-40" type="danger" plain @click="clearCache">
          <i class="i-heroicons:trash mr-1" />
          清除配置缓存
        </ElButton>
      </div>
    </div>

    <!-- 个人信息模块 -->
    <div class="border-b border-gray-100 p-6">
      <UserProfile />
    </div>

    <!-- 第三方账号模块 -->
    <div class="p-6">
      <ThirdPartyAccount />
    </div>
  </div>
</template>
