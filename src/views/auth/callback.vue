<script setup lang="ts">
import { ElLoading } from 'element-plus'

const route = useRoute()
const authStore = useAuthStore()
const showBindForm = ref(false)
const authUser = ref<AuthUser | undefined>(undefined)

const { source } = route.params as { source: ThirdPartySource }
const { code, state } = route.query as { code: string, state: string }

const data: LoginByThirdParams = { source, code, state }

/**
 * 处理第三方登录回调
 */
async function handleThirdPartyLogin(data: LoginByThirdParams) {
  const loadingInstance = ElLoading.service({
    lock: true,
    text: '正在处理第三方登录回调...',
  })

  try {
    await authStore.loginByScanThird(data)
  }
  catch (error: any) {
    // 未绑定账号，需单独处理
    if (error?.code === 6009) {
      showBindForm.value = true
      authUser.value = error.data
    }
  }
  finally {
    loadingInstance.close()
  }
}

onMounted(() => {
  handleThirdPartyLogin(data)
})
</script>

<template>
  <div
    class="relative min-h-screen flex items-center justify-center overflow-hidden bg-[url('~/assets/background.jpg')] bg-cover bg-center"
  >
    <div
      v-if="showBindForm"
      class="max-w-sm min-w-sm w-full flex flex-col items-center rounded-3xl bg-white px-10 py-10 shadow-lg sm:max-w-md"
    >
      <div class="mb-6 flex flex-col items-center">
        <img src="/favicon.ico" class="mb-2 h-20 w-auto" alt="logo">
        <div class="text-xl text-blue-600 font-semibold sm:text-2xl">
          {{ useEnv.VITE_TITLE }}
        </div>
      </div>
      <LoginSmsCode mode="bind" :auth-user="authUser" />
    </div>
  </div>
</template>
