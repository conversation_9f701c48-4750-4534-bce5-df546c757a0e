<script setup lang="ts">
import * as dd from 'dingtalk-jsapi'
import { ElMessage } from 'element-plus'

const authStore = useAuthStore()
const loading = ref(false)

// 是否不在钉钉环境 true 不在
const notInDingTalk = computed(() => dd.env.platform === 'notInDingTalk')

/**
 * 处理钉钉授权登录
 * 使用钉钉 JSAPI 获取授权码，然后调用登录接口
 */
function handleDingtalkAuth() {
  if (notInDingTalk.value) {
    ElMessage.warning('请在钉钉客户端中打开')
    return
  }
  if (!import.meta.env.VITE_DINGTALK_CORPID) {
    ElMessage.error('钉钉企业 ID 未配置，请联系管理员')
    return
  }

  loading.value = true
  const params: any = {
    corpId: import.meta.env.VITE_DINGTALK_CORPID, // 企业 id
    onSuccess: async (info: any) => {
      try {
        // 获取到授权码，调用登录接口
        await authStore.loginByDingTalk(info.code)
      }
      finally {
        loading.value = false
      }
    },
    onFail: (err: any) => {
      console.error('获取钉钉授权码失败：', err)
      ElMessage.error('获取钉钉授权码失败，请稍后重试')
      loading.value = false
    },
  }
  dd.runtime.permission.requestAuthCode(params)
}

onMounted(() => {
  dd.ready(() => {
    handleDingtalkAuth()
  })
  dd.error((err: any) => {
    console.error('钉钉 JSAPI 加载失败：', err)
    ElMessage.error('钉钉接口加载失败，请稍后重试')
  })
})
</script>

<template>
  <div
    class="relative min-h-screen flex items-center justify-center overflow-hidden from-blue-200 via-blue-300 to-blue-400 bg-gradient-to-tr"
  >
    <div
      class="absolute bottom-0 left-0 h-[40vw] w-[40vw] rounded-t-full from-blue-400 to-blue-200 bg-gradient-to-tr opacity-60 -z-10"
    />
    <div
      class="absolute right-0 top-0 h-[35vw] w-[50vw] rounded-b-full from-blue-300 to-blue-100 bg-gradient-to-br opacity-70 -z-10"
    />
    <div class="max-w-sm min-w-sm w-full flex flex-col items-center rounded-3xl bg-white px-10 py-10 shadow-lg sm:max-w-md">
      <div class="mb-6 flex flex-col items-center">
        <img src="/favicon.ico" class="mb-2 h-20 w-auto" alt="logo">
        <div class="text-xl text-blue-500 font-semibold sm:text-2xl">
          钉钉授权登录
        </div>
      </div>

      <div class="w-full flex flex-col items-center justify-center">
        <div v-if="loading" class="py-8 text-center">
          <div class="i-ri:loader-4-line mb-4 animate-spin text-4xl text-blue-500" />
          <p class="text-gray-600">
            正在进行钉钉授权登录，请稍候...
          </p>
        </div>
        <div v-else class="py-8 text-center">
          <div class="i-ri:dingding-fill mb-4 text-6xl text-hex-2E88FF" />
          <p class="mb-4 text-gray-600">
            请确保您已在钉钉客户端中登录
          </p>
          <button
            class="w-full rounded-lg bg-blue-500 px-4 py-2 text-white transition-colors hover:bg-blue-600"
            @click="handleDingtalkAuth"
          >
            重新获取授权
          </button>
        </div>
      </div>
    </div>
  </div>
</template>
