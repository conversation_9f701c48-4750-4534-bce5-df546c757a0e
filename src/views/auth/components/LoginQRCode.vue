<script setup lang="ts">
const qrCodeUrl = ref<string | null>(null)
const errorOccurred = ref(false)

/**
 * 获取用于登录的二维码 URL
 */
async function fetchQRCodeForLogin(source: ThirdPartySource = 'wechat_open') {
  try {
    const { url } = await ApiGetThirdUrl(source)
    if (url) {
      qrCodeUrl.value = url
      errorOccurred.value = false
    }
  }
  catch (error) {
    errorOccurred.value = true
    qrCodeUrl.value = null
  }
}

onMounted(() => {
  fetchQRCodeForLogin('wechat_open')
})
</script>

<template>
  <div class="flex flex-col items-center justify-center">
    <div class="h-46 w-42 flex items-center justify-center overflow-hidden">
      <iframe
        v-if="qrCodeUrl" :src="qrCodeUrl" frameborder="0" class="block h-full w-full overflow-hidden"
        title="登录二维码" sandbox="allow-scripts allow-same-origin allow-top-navigation" scrolling="no"
      />
      <p v-else-if="errorOccurred" class="p-2 text-center text-xs text-red-500">
        二维码加载失败，请刷新页面重试
      </p>
      <p v-else class="text-xs text-gray-400">
        正在加载...
      </p>
    </div>
    <p class="mt-1 text-xs text-gray-400">
      微信快捷登录
    </p>
  </div>
</template>
