<script setup lang="ts">
interface Props {
  mode?: 'login' | 'bind' // 登录模式或绑定模式
  authUser?: AuthUser
}

const props = withDefaults(defineProps<Props>(), {
  mode: 'login',
})

const formRef = ref()
const rememberMe = ref(true)
const form = reactive<LoginBySmsCodeParams>({
  username: !useEnv.isProd ? useEnv.VITE_DEV_Username : '',
  smsCode: '',
})

const sending = ref(false)
const countdown = ref(0)
let timer: number | undefined
const SMS_CODE_COUNTDOWN_SECONDS = 120 // 短信验证码倒计时秒数

const rules = {
  username: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入合法的手机号', trigger: 'blur' },
  ],
  smsCode: [
    { required: true, message: '请输入验证码', trigger: 'blur' },
  ],
}

const buttonText = computed(() => {
  return props.mode === 'login' ? '登 录' : '绑 定'
})

/**
 * 启动倒计时定时器
 * @param seconds 倒计时秒数
 */
function startCountdown(seconds: number) {
  countdown.value = seconds
  // 保存倒计时结束时间到缓存
  useMyStorage.set('smsCountdownEndTime', Date.now() + seconds * 1000)

  // 清除可能存在的旧定时器
  if (timer) {
    clearInterval(timer)
  }

  timer = setInterval(() => {
    countdown.value--
    if (countdown.value <= 0) {
      clearInterval(timer)
      timer = undefined
      useMyStorage.remove('smsCountdownEndTime')
    }
  }, 1000)
}

/**
 * 发送验证码
 */
async function onSendCode() {
  if (sending.value || countdown.value > 0)
    return

  formRef.value.validateField('username', async (isValid: boolean) => {
    if (isValid) {
      // 用户名/手机号验证通过后再发送短信
      sending.value = true
      try {
        await ApiSendSmsCode({ mobile: form.username, type: props.mode })
        ElMessage.success('验证码已发送')
        startCountdown(SMS_CODE_COUNTDOWN_SECONDS) // 启动倒计时
      }
      finally {
        sending.value = false
      }
    }
  })
}

/**
 * 提交表单
 */
async function onSubmit() {
  formRef.value.validate(async (valid: boolean) => {
    if (valid) {
      try {
        if (props.mode === 'login') {
          await useAuthStore().loginBySmsCode(form) // 登录
          // 只在登录模式下保存用户名，保持与密码登录相同的存储结构
          if (rememberMe.value) {
            // 获取已存储的登录信息，保留密码
            const savedLoginInfo = await useMyStorage.get('loginInfo') as { username: string, password: string } | null
            const password = savedLoginInfo?.password || ''
            useMyStorage.set('loginInfo', { username: form.username, password })
          }
          else {
            useMyStorage.remove('loginInfo')
          }
        }
        else {
          await useAuthStore().loginByBindThird({ ...form, authUser: props.authUser }) // 绑定
        }
      }
      catch (err: any) {
        ElMessage.error(err?.detail || '操作失败')
      }
    }
  })
}

// 初始化时读取缓存中的倒计时
onMounted(async () => {
  // 读取缓存中的倒计时结束时间
  const endTime = await useMyStorage.get('smsCountdownEndTime') as number | null
  if (endTime) {
    const now = Date.now()
    const remain = Math.floor((endTime - now) / 1000)
    if (remain > 0) {
      startCountdown(remain)
    }
    else {
      useMyStorage.remove('smsCountdownEndTime')
    }
  }

  // 只在登录模式下读取用户名
  if (props.mode === 'login') {
    const savedLoginInfo = await useMyStorage.get('loginInfo')
    if (savedLoginInfo) {
      form.username = (savedLoginInfo as { username: string }).username
      rememberMe.value = true
    }
  }
})
</script>

<template>
  <ElForm ref="formRef" :model="form" :rules="rules" class="w-full" @submit.prevent @keydown.enter="onSubmit">
    <ElFormItem prop="username">
      <ElInput v-model.trim="form.username" placeholder="请输入手机号" size="large" clearable>
        <template #prefix>
          <i class="i-heroicons:user" />
        </template>
      </ElInput>
    </ElFormItem>
    <ElFormItem prop="smsCode">
      <ElInput v-model.trim="form.smsCode" placeholder="请输入验证码" size="large">
        <template #prefix>
          <i class="i-heroicons:chat-bubble-left-ellipsis" />
        </template>
        <template #append>
          <ElButton
            size="large" style="width: 110px" :loading="sending" :disabled="sending || countdown > 0"
            @click="onSendCode"
          >
            <span v-if="sending">发送中</span>
            <span v-else-if="countdown > 0">{{ countdown }}s</span>
            <span v-else>发送验证码</span>
          </ElButton>
        </template>
      </ElInput>
    </ElFormItem>
    <!-- 只在登录模式下显示记住账号 -->
    <ElFormItem v-if="props.mode === 'login'">
      <ElCheckbox v-model="rememberMe">
        记住账号
      </ElCheckbox>
    </ElFormItem>
    <ElFormItem>
      <ElButton type="primary" class="w-full" size="large" @click="onSubmit">
        {{ buttonText }}
      </ElButton>
    </ElFormItem>
  </ElForm>
</template>
