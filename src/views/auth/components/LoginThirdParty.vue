<script setup lang="ts">
/**
 * 第三方登录方式接口
 */
interface ThirdLoginMethod {
  id: ThirdPartySource
  name: string
  icon: string
  color?: string // 可选颜色，用于图标
}

/**
 * 第三方登录方式列表
 */
const ThirdLoginMethods: ThirdLoginMethod[] = [
  {
    id: 'wechat_open',
    name: '微信登录',
    icon: 'i-ri:wechat-fill',
    color: 'text-hex-07C160',
  },
  {
    id: 'dingtalk',
    name: '钉钉登录',
    icon: 'i-ri:dingding-fill',
    color: 'text-hex-2E88FF',
  },
]

/**
 * 打开第三方登录 QRCode，扫码后会跳转回调地址 /social-login/:source
 * @param source - 登录源 ('wechat_open', 'dingtalk')
 */
async function openThirdPartyLoginQRCode(source: ThirdPartySource) {
  try {
    const { url } = await ApiGetThirdUrl(source)
    if (url) {
      location.href = url
    }
  }
  catch (error) {
    ElMessage.error('获取登录链接失败，请稍后重试')
  }
}
</script>

<template>
  <div class="w-full">
    <div class="my-4 w-full flex items-center">
      <div class="h-px flex-1 bg-gray-200" />
      <span class="mx-2 text-xs text-gray-400">其他账号登录</span>
      <div class="h-px flex-1 bg-gray-200" />
    </div>
    <div class="w-full flex justify-center gap-4">
      <template v-for="method in ThirdLoginMethods" :key="method.id">
        <i
          :class="`${method.icon} ${method.color} cursor-pointer text-2xl transition-all duration-300 hover:scale-110`"
          :title="method.name"
          @click="openThirdPartyLoginQRCode(method.id)"
        />
      </template>
    </div>
  </div>
</template>
