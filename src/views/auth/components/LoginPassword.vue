<script setup lang="ts">
import { ElMessage } from 'element-plus'

const formRef = ref()
const rememberMe = ref(true)
const form = reactive<LoginByPasswordParams>({
  username: !useEnv.isProd ? useEnv.VITE_DEV_Username : '',
  password: !useEnv.isProd ? useEnv.VITE_DEV_Password : '',
  uuid: '', // 图形 uuid
  code: '', // 图形验证码
})

const rules = {
  username: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入合法的手机号', trigger: 'blur' },
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
  ],
  code: [
    { required: true, message: '请输入验证码', trigger: 'blur' },
  ],
}

const captchaImg = ref('')

async function getCaptcha() {
  const res = await ApiGetCaptchaImage()
  form.uuid = res.uuid
  captchaImg.value = `data:image/png;base64,${res.img}`
}

onMounted(async () => {
  getCaptcha()
  // 从缓存中读取登录信息
  const savedLoginInfo = await useMyStorage.get('loginInfo')
  if (savedLoginInfo) {
    const loginInfo = savedLoginInfo as { username: string, password: string }
    form.username = loginInfo.username
    form.password = loginInfo.password
    rememberMe.value = true
  }
})

function onSubmit() {
  formRef.value.validate(async (valid, fields) => {
    if (!valid)
      return
    try {
      await useAuthStore().loginByPassword(form)
      // 根据 rememberMe 判断保存还是清除登录信息
      if (rememberMe.value)
        useMyStorage.set('loginInfo', { username: form.username, password: form.password })
      else
        useMyStorage.remove('loginInfo')
    }
    catch (err: any) {
      if (err?.detail === '验证码已失效') {
        ElMessage.error('图片验证码过期，已自动刷新验证码')
        return getCaptcha()
      }
      ElMessage.error(err?.detail)
    }
  })
}
</script>

<template>
  <ElForm ref="formRef" :model="form" :rules="rules" class="w-full" @submit.prevent @keydown.enter="onSubmit">
    <ElFormItem prop="username">
      <ElInput v-model.trim="form.username" placeholder="请输入手机号" size="large" clearable trim>
        <template #prefix>
          <i class="i-heroicons:user" />
        </template>
      </ElInput>
    </ElFormItem>
    <ElFormItem prop="password">
      <ElInput v-model.trim="form.password" placeholder="请输入密码" size="large" show-password>
        <template #prefix>
          <i class="i-heroicons:lock-closed" />
        </template>
      </ElInput>
    </ElFormItem>
    <ElFormItem prop="code">
      <div class="w-full flex items-center">
        <ElInput v-model.trim="form.code" placeholder="请输入图形验证码" size="large" class="flex-1">
          <template #prefix>
            <i class="i-heroicons:shield-check" />
          </template>
        </ElInput>
        <img :src="captchaImg" class="ml-2 h-[38px] cursor-pointer" alt="点击刷新验证码" @click="getCaptcha">
      </div>
    </ElFormItem>
    <ElFormItem>
      <ElCheckbox v-model="rememberMe">
        记住账号
      </ElCheckbox>
      <!-- <span class="ml-auto cursor-pointer select-none text-xs text-gray-400">忘记密码？</span> -->
    </ElFormItem>
    <ElFormItem>
      <ElButton type="primary" class="w-full" size="large" @click="onSubmit">
        登 录
      </ElButton>
    </ElFormItem>
  </ElForm>
</template>
