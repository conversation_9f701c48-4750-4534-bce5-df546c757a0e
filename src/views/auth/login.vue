<script setup lang="ts">
import LoginPassword from './components/LoginPassword.vue'
import LoginQRCode from './components/LoginQRCode.vue'
import LoginSmsCode from './components/LoginSmsCode.vue'
import LoginThirdParty from './components/LoginThirdParty.vue'

const loginType = ref('password')
function switchType(type: 'password' | 'code') {
  loginType.value = type
}
const loginTypeComponent = computed(() => loginType.value === 'password' ? LoginPassword : LoginSmsCode)
</script>

<template>
  <div
    class="relative min-h-screen flex items-center justify-center overflow-hidden bg-[url('~/assets/background.jpg')] bg-blue-800 bg-cover bg-center"
  >
    <div
      class="max-w-sm w-full flex flex-col items-center justify-center gap-x-0 rounded-3xl bg-white p-6 shadow-lg md:max-w-3xl md:flex-row md:gap-x-8 md:p-10"
    >
      <div class="max-w-sm w-full flex flex-col items-center">
        <div class="mb-6 flex flex-col items-center">
          <img src="/favicon.ico" class="mb-2 h-20 w-auto" alt="logo">
          <div class="text-xl text-blue-600 font-semibold sm:text-2xl">
            {{ useEnv.VITE_TITLE }}
          </div>
        </div>
        <div class="mb-4 w-full flex justify-center">
          <div class="relative mx-4 w-full flex gap-x-2 rounded-full bg-blue-100/50 p-1 text-sm">
            <div
              class="absolute left-0 top-0 h-full w-1/2 rounded-full bg-blue-400 shadow transition-transform duration-300 ease-in-out"
              :style="{ transform: loginType === 'password' ? 'translateX(0%)' : 'translateX(100%)' }"
            />

            <button
              class="relative z-10 flex flex-1 items-center justify-center gap-x-1 p-1 transition-colors duration-300"
              :class="loginType === 'password' ? 'text-white' : 'text-gray-400'" @click="switchType('password')"
            >
              <i class="i-heroicons:lock-closed" />
              密码登录
            </button>
            <button
              class="relative z-10 flex flex-1 items-center justify-center gap-x-1 p-1 transition-colors duration-300"
              :class="loginType === 'code' ? 'text-white' : 'text-gray-400'" @click="switchType('code')"
            >
              <i class="i-heroicons:chat-bubble-left-ellipsis" />
              验证码登录
            </button>
          </div>
        </div>
        <Transition name="slide-x" mode="out-in">
          <component :is="loginTypeComponent" key="loginType" class="w-full" />
        </Transition>

        <LoginThirdParty />
      </div>

      <div class="mx-4 hidden h-64 w-px bg-gray-200 md:block" />

      <div class="mt-8 flex flex-col items-center justify-center md:mt-0">
        <LoginQRCode />
      </div>
    </div>
  </div>
</template>

<style scoped>
.slide-x-enter-active,
.slide-x-leave-active {
  transition: all 0.4s cubic-bezier(0.55, 0, 0.1, 1);
}
.slide-x-enter-from {
  opacity: 0;
  transform: translateX(40px);
}
.slide-x-leave-to {
  opacity: 0;
  transform: translateX(-40px);
}
</style>
