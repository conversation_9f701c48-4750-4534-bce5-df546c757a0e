<script setup lang="ts">
const appsStore = useAppsStore()

// 应用列表数据
const appList = ref<AppItem[]>([])
const filteredAppList = ref<AppItem[]>([])

// 搜索和筛选相关
const searchQuery = ref('')
const sortOptions = [
  { label: '默认排序', value: 'default' },
  { label: '收藏排序', value: 'isTop' },
  { label: '点击量排序', value: 'clickNum' },
  // { label: '名称排序', value: 'name' },
  // { label: '级别排序', value: 'level' },
]

// 级别筛选选项
const levelOptions = [
  { label: '全部', value: 'all' },
  // { label: '常用应用', value: '9' },
  // { label: '国家级应用', value: '4' },
  // { label: '省级应用', value: '3' },
  { label: '市级应用', value: '2' },
  { label: '区级应用', value: '1' },
  { label: '校级应用', value: '0' },
]

// 筛选和排序状态
const filterState = reactive({
  currentSort: 'default',
  currentLevel: 'all',
})

const getTailwindColorByName = appsStore.getTailwindColorByName
const formatAppLevel = appsStore.formatAppLevel

/**
 * 获取应用列表数据
 */
async function loadAppsData() {
  const { list } = await appsStore.fetchApps({
    page: 1,
    limit: 100, // 获取更多数据，无需分页
    sort: 'name:asc', // 按名称升序排序
    // sort: 'bind_user:desc,is_top:desc,name:asc', // 先按收藏排序，再按点击量降序排序，再按名称升序排序
  })
  appList.value = list
  applyFiltersAndSort() // 初始应用筛选和排序
}

/**
 * 应用筛选和排序
 */
function applyFiltersAndSort() {
  // 先筛选
  let result = [...appList.value]

  // 搜索筛选
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    result = result.filter(app =>
      app.appName.toLowerCase().includes(query)
      || (app.detail && app.detail.toLowerCase().includes(query)),
    )
  }

  // 级别筛选
  if (filterState.currentLevel !== 'all') {
    result = result.filter(app => app.level === filterState.currentLevel)
  }

  // 排序
  if (filterState.currentSort === 'clickNum') {
    result.sort((a, b) => b.clickNum - a.clickNum)
  }
  else if (filterState.currentSort === 'name') {
    result.sort((a, b) => a.appName.localeCompare(b.appName))
  }
  else if (filterState.currentSort === 'level') {
    result.sort((a, b) => b.level.localeCompare(a.level))
  }
  else if (filterState.currentSort === 'isTop') {
    result.sort((a, b) => b.isTop === '1' ? 1 : -1)
  }

  filteredAppList.value = result
}

// 监听筛选和排序变化
watch([searchQuery, filterState], () => {
  applyFiltersAndSort()
})

// 使用 usePageConfig Hook 管理筛选和排序的持久化
usePageConfig('appPage', filterState, loadAppsData)

/**
 * 收藏应用
 * @param appId 应用 ID
 */
async function pinApp(appId: string) {
  await ApiPinApp(appId)
  ElMessage.success(appId === '1' ? '应用收藏成功' : '应用取消收藏成功')
  loadAppsData() // 重新加载应用列表以反映收藏状态
}

/**
 * 解绑应用
 * @param appId 应用 ID
 */
async function unbindApp(appId: string) {
  await ApiUnbindUser(appId)
  ElMessage.success('应用解绑成功')
  loadAppsData() // 重新加载应用列表以反映解绑状态
}

// 将选定账号设置为应用默认绑定账号
async function handleUserClick(appId: string, user: string) {
  await ApiBindDefaultUser({ appId, bindUser: user })
  ElMessage.success(`已将 ${user} 设置为默认绑定账号`)
  loadAppsData()
}

// 应用点击开始检测绑定状态并进行跳转
async function handleAppClick(item) {
  await appsStore.handleAppOpening(item)
  loadAppsData()
}
</script>

<template>
  <div class="rounded-lg bg-white px-5 py-6 shadow sm:px-6">
    <div class="border-b border-gray-200 pb-5">
      <h3 class="text-lg text-gray-800 font-medium leading-6">
        应用中心
      </h3>
      <!-- 补充功能栏 -->
      <div class="mt-4 flex items-center justify-between">
        <!-- 搜索框 -->
        <ElInput v-model.trim="searchQuery" style="width: 240px" placeholder="搜索应用..." />
        <!-- 筛选和排序 -->
        <div class="flex items-center space-x-4">
          <div class="flex items-center gap-2">
            <span class="w-12 text-sm text-gray-500">级别：</span>
            <ElSelect v-model="filterState.currentLevel" style="width: 100px">
              <ElOption
                v-for="option in levelOptions" :key="option.value" :label="option.label"
                :value="option.value"
              />
            </ElSelect>
          </div>
          <div class="flex items-center gap-2">
            <span class="w-12 text-sm text-gray-500">排序：</span>
            <ElSelect v-model="filterState.currentSort" style="width: 100px">
              <ElOption v-for="option in sortOptions" :key="option.value" :label="option.label" :value="option.value" />
            </ElSelect>
          </div>
        </div>
      </div>
    </div>

    <!-- 应用列表 -->
    <div class="mt-6 min-h-100">
      <div
        v-if="filteredAppList.length > 0"
        class="grid grid-cols-1 gap-4 md:grid-cols-3 sm:grid-cols-2 xl:grid-cols-4"
      >
        <div
          v-for="item in filteredAppList" :key="item.appId"
          class="relative flex flex-col cursor-pointer overflow-hidden border border-gray-200 rounded-lg bg-white transition duration-300 ease-in-out hover:border-gray-300 hover:shadow-md"
          @click="appsStore.handleAppOpening(item)"
        >
          <!-- 灰色蒙版 -->
          <!-- <div v-if="!item.bindUser" class="absolute inset-0 z-10 bg-gray-100 opacity-50" /> -->

          <!-- 应用头部 -->
          <div class="relative z-20 flex items-center justify-between border-b border-gray-100 p-4">
            <div class="flex items-center space-x-3">
              <div
                :class="getTailwindColorByName(item.color, false)"
                class="h-10 w-10 flex flex-shrink-0 items-center justify-center rounded-md transition-all duration-400"
              >
                <Icon :icon="item.icon" class="size-6" aria-hidden="true" />
              </div>
              <div>
                <h3 class="text-sm text-gray-800 font-medium">
                  {{ item.appName }}
                </h3>
                <div class="flex items-center gap-2 text-xs text-gray-400">
                  <span>{{ formatAppLevel(item.level) }}</span>
                  <span class="flex items-center gap-0.5">
                    <i class="i-heroicons:eye size-3.5 text-gray-400" />
                    {{ item.clickNum }}
                  </span>
                </div>
              </div>
            </div>
            <div class="flex items-center gap-2">
              <!-- 收藏按钮/图标 (统一位置) -->
              <button
                class="flex items-center justify-center rounded-full p-1 transition-colors hover:bg-gray-50"
                :title="item.isTop === '1' ? '取消收藏' : '收藏应用'" @click.stop="pinApp(item.appId)"
              >
                <i
                  :class="[
                    item.isTop === '1'
                      ? 'i-heroicons:star-solid text-amber-500'
                      : 'i-heroicons:star text-gray-400 hover:text-amber-500',
                  ]" class="size-6"
                />
              </button>
              <!-- <i class="i-heroicons:arrow-up-right-solid h-5 w-5 text-gray-400" /> -->
            </div>
          </div>

          <!-- 应用详情 -->
          <div class="relative z-20 flex-1 p-4">
            <p class="line-clamp-3 min-h-10 text-sm text-gray-500 leading-6">
              {{ item.detail || '暂无详细描述' }}
            </p>
            <!--
            <div class="mt-4 flex flex-wrap gap-2">
              <span
                class="inline-flex items-center rounded-md bg-blue-50 px-2 py-1 text-xs text-blue-700 font-medium ring-1 ring-blue-700/10 ring-inset"
              >
                {{ formatBindType(item.bindType) }}
              </span>
              <span
                class="inline-flex items-center rounded-md bg-purple-50 px-2 py-1 text-xs text-purple-700 font-medium ring-1 ring-purple-700/10 ring-inset"
              >
                {{ formatAppLevel(item.level) }}
              </span>
            </div> -->
          </div>

          <!-- 应用底部 -->
          <div class="relative z-20 bg-gray-50 px-4 py-3 text-xs text-gray-500" @click.stop>
            <!-- 账号信息区域 -->
            <!-- 已绑定状态 -->
            <div v-if="item.bindUser" class="flex items-center justify-between">
              <div class="flex items-center gap-2">
                <i class="i-heroicons:user size-4 text-gray-500" />
                <span>{{ item.bindUser }}</span>

                <!-- 多账号切换图标 -->
                <ElPopover
                  v-if="item.bindUserList && item.bindUserList.length > 1" placement="right" :width="80"
                  trigger="hover"
                >
                  <template #reference>
                    <i
                      class="i-heroicons:arrow-path-rounded-square-16-solid size-5 cursor-pointer text-blue-500 hover:text-blue-700"
                    />
                  </template>

                  <div class="text-xs text-gray-500">
                    <div class="px-2 pb-1.5 font-medium">
                      切换默认账号
                      <i class="i-heroicons:chevron-down" />
                    </div>
                    <div
                      v-for="user in item.bindUserList.filter(u => u !== item.bindUser)" :key="user"
                      class="flex cursor-pointer items-center gap-2 rounded px-2 py-1.5 hover:bg-blue-100/50"
                      @click.stop="handleUserClick(item.appId, user)"
                    >
                      <span>{{ user }}</span>
                    </div>
                  </div>
                </ElPopover>
              </div>

              <!-- 解绑按钮 -->
              <ElPopconfirm title="确定要解绑该应用吗？" placement="bottom-end" width="200" @confirm="unbindApp(item.appId)">
                <template #reference>
                  <ElButton type="danger" plain size="small" @click.stop>
                    <i class="i-heroicons:link-slash mr-1" />解绑
                  </ElButton>
                </template>
              </ElPopconfirm>
            </div>

            <!-- 第三方应用 -->
            <div v-if="item.bindType === '3'" class="flex items-center justify-end">
              <ElButton type="primary" plain size="small" @click.stop="handleAppClick(item)">
                <i class="i-heroicons:arrow-up-right-solid mr-1" />跳转
              </ElButton>
            </div>

            <!-- 未绑定状态且不是第三方应用 -->
            <div v-if="!item.bindUser && item.bindType !== '3'" class="flex items-center justify-between">
              <div class="flex items-center gap-2">
                <i class="i-heroicons:exclamation-triangle size-4 text-gray-300" />
                <span class="text-gray-300">未绑定</span>
              </div>
              <!-- 绑定按钮 -->
              <ElButton type="success" plain size="small" @click.stop="handleAppClick(item)">
                <i
                  :class="item.bindType === '1' ? 'i-heroicons:lock-closed' : 'i-heroicons:device-phone-mobile'"
                  class="mr-1"
                />绑定
              </ElButton>
            </div>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-else class="flex flex-col items-center justify-center py-8 text-gray-400">
        <i class="i-heroicons:document-text mb-2 size-14" />
        <h3 class="text-sm font-medium">
          未找到应用
        </h3>
        <p class="mt-1 text-sm">
          尝试调整搜索或筛选条件
        </p>
      </div>
    </div>
  </div>

  <!-- 绑定应用对话框组件 -->
  <BindAppDialog v-model:visible="appsStore.bindDialogVisible" :app="appsStore.currentApp" />
</template>
