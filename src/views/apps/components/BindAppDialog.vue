<script setup lang="ts">
import type { FormRules } from 'element-plus'

const props = defineProps<{
  visible: boolean
  app: AppItem | FunctionItem | null
}>()

const emit = defineEmits<{
  'update:visible': [value: boolean]
}>()

const appsStore = useAppsStore()
const getTailwindColorByName = appsStore.getTailwindColorByName

const bindFormRef = ref()
const bindForm = reactive<BindUserParams>({
  appId: '',
  name: '',
  password: '',
})

// 监听 props.app 变化，自动设置 appId
watch(() => props.app, (newApp) => {
  if (newApp) {
    bindForm.appId = newApp.appId
  }
}, { immediate: true })

// 表单验证规则
const bindFormRules = reactive<FormRules>({
  name: [
    { required: true, message: '请输入账号', trigger: 'blur' },
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
  ],
})

// 对话框可见性控制
const dialogVisible = computed({
  get: () => props.visible,
  set: value => emit('update:visible', value),
})

// 提交绑定应用表单
function submitBindForm() {
  bindFormRef.value.validate(async (valid) => {
    if (valid) {
      await appsStore.bindAppByPassword(bindForm)
      dialogVisible.value = false
    }
  })
}

// 重置绑定表单
function resetBindForm() {
  if (bindFormRef.value) {
    bindFormRef.value.resetFields()
  }
}

// 处理取消操作
function handleCancel() {
  emit('update:visible', false)
}
</script>

<template>
  <!-- 绑定应用对话框 -->
  <ElDialog
    v-model="dialogVisible" class="!px-6 !pb-6" :title="props.app ? `绑定应用 - ${props.app.appName}` : '绑定应用'"
    width="400" destroy-on-close @closed="resetBindForm"
  >
    <div v-if="props.app" class="mb-6 flex items-center">
      <div
        class="mr-4 h-12 w-12 flex items-center justify-center rounded-md transition-all duration-400"
        :class="getTailwindColorByName(props.app.color, false)"
      >
        <Icon :icon="props.app.icon" class="size-7" aria-hidden="true" />
      </div>
      <div>
        <h3 class="text-base text-gray-800 font-medium">
          {{ props.app.appName }}
        </h3>
        <p class="text-sm text-gray-500">
          {{ 'detail' in props.app ? props.app.detail : '暂无详细描述' }}
        </p>
      </div>
    </div>

    <ElForm ref="bindFormRef" :model="bindForm" :rules="bindFormRules" label-position="top" class="mt-4">
      <ElFormItem label="账号" prop="name">
        <ElInput v-model.trim="bindForm.name" autocomplete="off" placeholder="请输入绑定应用账号" />
      </ElFormItem>
      <ElFormItem label="密码" prop="password">
        <ElInput
          v-model="bindForm.password" type="password" autocomplete="off" placeholder="请输入绑定应用密码"
          show-password
        />
      </ElFormItem>
    </ElForm>

    <template #footer>
      <div class="flex justify-between gap-3">
        <ElButton @click="handleCancel">
          取消绑定
        </ElButton>
        <ElButton type="primary" @click="submitBindForm">
          确认绑定
        </ElButton>
      </div>
    </template>
  </ElDialog>
</template>
