// =================================================================================
// 密码登录
// =================================================================================

/**
 * 密码登录
 * @param data - 登录凭证
 * @returns { token: string } 包含登录令牌的对象
 */
export function ApiLoginByPassword(data: LoginByPasswordParams): Promise<{ token: string }> {
  return useRequest({
    method: 'post',
    url: '/loginByPassword',
    data: {
      ...data,
      password: useCrypto.encrypt(data.password),
    },
    showErrorMsg: false, // 不显示错误提示，在业务中具体处理
  } as RequestConfig)
}

/**
 * 获取图形验证码 (用于密码登录)
 * @returns 包含图形验证码图片数据和唯一标识
 */
export function ApiGetCaptchaImage(): Promise<CaptchaImageResponse> {
  return useRequest({
    method: 'get',
    url: '/captchaImage',
  })
}

// =================================================================================
// 验证码登录
// =================================================================================

/**
 * 验证码登录
 * @param data - 登录凭证
 * @returns { token: string } 包含登录令牌的对象
 */
export function ApiLoginBySmsCode(data: LoginBySmsCodeParams): Promise<{ token: string }> {
  return useRequest({
    method: 'post',
    url: '/loginByCode',
    data,
  })
}

/**
 * 发送短信验证码 (用于验证码登录)
 * @param data - 请求参数
 */
export function ApiSendSmsCode(data: SmsCodeParams): Promise<void> {
  return useRequest({
    method: 'post',
    url: '/sms/code',
    data,
  })
}

// =================================================================================
// 第三方登录
// =================================================================================

/**
 * 获取第三方绑定链接 - 扫码后获得 code 和 state 再调用 ApiLoginByThird
 * @param source - 来源参数，仅限 'wechat_open' 或 'dingtalk'
 * @returns { url: string } 包含授权链接的对象
 */
export function ApiGetThirdUrl(source: ThirdPartySource): Promise<{ url: string }> {
  return useRequest({
    method: 'get',
    url: `/auth/binding/${source}`,
  })
}

/**
 * 第三方登录 - 登录后直接绑定，未登录则提示绑定再调用 ApiLoginByBindThird 进行绑定
 * @param params - 登录参数
 * @returns { token: string } 包含登录令牌的对象
 */
export function ApiLoginByThird(params: LoginByThirdParams): Promise<{ token: string }> {
  return useRequest({
    method: 'get',
    url: `/auth/social-login/${params.source}`,
    params: {
      code: params.code,
      state: params.state,
    },
    showErrorMsg: false, // 不显示错误提示，在业务中具体处理
  } as RequestConfig)
}

/**
 * 第三方登录 - 首次扫码后绑定
 * @param data - 绑定参数
 * @returns { token: string } 包含登录令牌的对象
 */
export function ApiLoginByBindThird(data: LoginByBindThirdParams): Promise<{ token: string }> {
  return useRequest({
    method: 'post',
    url: '/auth/binding',
    data,
  })
}

/**
 * 解除第三方绑定
 * @param authId - 授权 ID
 */
export function ApiUnbindThird(authId: string): Promise<void> {
  return useRequest({
    method: 'delete',
    url: `/auth/unlock/${authId}`,
  })
}

// =================================================================================
// 钉钉内部工作台登录
// =================================================================================

/**
 * 钉钉内部工作台登录
 * @param code - 钉钉 SKD 返回的 code，免登授权码可以获取用户身份
 * @returns { token: string } 包含登录令牌的对象
 */
export function loginByDingtalk(code: string): Promise<{ token: string }> {
  return useRequest({
    method: 'get',
    url: '/dingtalk/login',
    params: { code },
  })
}

// =================================================================================
// 用户及其他
// =================================================================================

/**
 * 获取用户信息
 */
export function ApiGetUserInfo(): Promise<UserInfo> {
  return useRequest({
    method: 'get',
    url: '/users',
  })
}

/**
 * 校验手机绑定状态
 * @param mobile - 手机号
 * @returns 返回手机号绑定的第三方平台状态，包含 wechat_open 和 dingtalk 两个布尔值
 * @deprecated
 */
export function ApiCheckMobileBind(mobile: string): Promise<CheckMobileBindResponse> {
  return useRequest({
    method: 'get',
    url: '/auth/check',
    params: { mobile },
  })
}
