/**
 * 获取应用列表
 * @param params - 查询参数
 */
export function ApiGetApps(params: ListRequestParams): Promise<ListResponse<AppItem>> {
  return useRequest({
    method: 'get',
    url: '/apps',
    params,
  })
}

/**
 * 应用/功能 点击量统计 - 已绑定应用才记录
 * @param appId - 应用 ID
 * @param functionId - 功能 ID，可选
 * @returns 前端静默处理，不关心错误，错误记录在后端日志中
 */
export function ApiRecordClick(appId: string, functionId?: string): Promise<void> {
  return useRequest({
    method: 'get',
    url: `/apps/click/${appId}`,
    params: { functionId },
  })
}

/**
 * 验证绑定状态并获取完整授权路径
 * 旧项目 /oauth/isBindUser + /oauth/code 两个接口的合并
 * @param appId - 应用 ID
 * @param functionId - 功能 ID，可选，如果传入则使用功能对应的跳转链接
 * @returns 其实只使用到了 fullRedirectUrl
 */
export function ApiCheckBindAndGetUrl(appId: string, functionId?: string): Promise<CheckBindAndGetUrlResponse> {
  return useRequest({
    method: 'get',
    url: '/oauth/status',
    params: { appId, functionId },
  })
}

/**
 * 绑定应用 - 绑定类型 '1': 账号密码绑定
 * @param data - 绑定数据
 * @returns { fullRedirectUrl: string } 包含完整重定向 URL 的对象
 */
export function ApiBindUserByPassword(data: BindUserParams): Promise<{ fullRedirectUrl: string }> {
  return useRequest({
    method: 'post',
    url: '/oauth/bindUser',
    data,
  })
}

/**
 * 绑定应用 - 绑定类型 '2': 手机号绑定
 * @param data - 绑定数据
 * @returns { fullRedirectUrl: string } 包含完整重定向 URL 的对象
 */
export function ApiBindUserByPhone(data: BindUserByPhoneParams): Promise<{ fullRedirectUrl: string }> {
  return useRequest({
    method: 'post',
    url: '/oauth/phone/bindUser',
    data,
  })
}

/**
 * 解绑应用
 * @param appId - 应用 ID
 */
export function ApiUnbindUser(appId: string): Promise<void> {
  return useRequest({
    method: 'get',
    url: '/oauth/unBindUser',
    params: { appId },
  })
}

/**
 * 应用收藏
 * @param appId - 应用 ID
 */
export function ApiPinApp(appId: string): Promise<void> {
  return useRequest({
    method: 'get',
    url: `/apps/top/${appId}`,
  })
}

/**
 * 设置应用默认绑定账号
 * 即从 bindUserList 列表中选取更新到到 bindUser 字段
 * @param data - 包含应用 ID 和绑定账号的对象
 * @param data.appId - 应用 ID
 * @param data.bindUser - 绑定账号
 */
export function ApiBindDefaultUser(data: { appId: string, bindUser: string }): Promise<void> {
  return useRequest({
    method: 'post',
    url: '/apps/bind',
    data,
  })
}

/**
 * 获取功能列表
 * @param params - 查询参数
 * @returns { list: FunctionItem[]; total: number } 包含功能列表和总数的对象
 */
export function ApiGetFunctions(params: ListRequestParams): Promise<ListResponse<FunctionItem>> {
  return useRequest({
    method: 'get',
    url: '/function',
    params,
  })
}
