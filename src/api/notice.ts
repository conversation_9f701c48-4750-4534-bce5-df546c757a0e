/**
 * 获取消息列表
 * @param params - 查询参数
 */
export function ApiGetNotices(params: ListRequestParams): Promise<ListResponse<NoticeItem>> {
  return useRequest({
    method: 'get',
    url: '/notices',
    params,
  })
}

/**
 * 获取消息详情 - 点击后标记为已读
 * @param noticeId - 通知 ID
 */
export function ApiGetNoticeDetail(noticeId: number): Promise<NoticeItem> {
  return useRequest({
    method: 'get',
    url: `/notices/${noticeId}`,
  })
}

/**
 * 获取常见问题列表
 * @param params - 查询参数
 */
export function ApiGetFaqs(params: ListRequestParams): Promise<ListResponse<FaqItem>> {
  return useRequest({
    method: 'get',
    url: '/questions',
    params,
  })
}
