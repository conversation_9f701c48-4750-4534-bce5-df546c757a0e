import { ElNotification } from 'element-plus'

export const useAuthStore = defineStore('auth', () => {
  const token = ref<string | null>(null)
  const router = useRouter()

  const initToken = () => {
    token.value = useMyCookies.get('token') as string | null
  }
  initToken()

  /**
   * 处理登录成功逻辑
   * @param tokenValue - 登录 token
   */
  const handleLoginSuccess = async (tokenValue: string) => {
    ElNotification({
      title: '登录成功',
      message: '正在跳转...',
      type: 'success',
      duration: 1000,
    })
    token.value = tokenValue
    useMyCookies.set('token', tokenValue) // 默认 1 天过期自动清理

    // 不用 await 等待，在后台获取，路由还会检查一遍
    useUserStore().fetchUserInfo()

    // 获取路由中的 redirect 参数，优先重定向，若无则跳转首页
    const redirect = router.currentRoute.value.query.redirect as string | undefined
    redirect ? router.push(redirect) : router.push({ name: 'Dashboard' })
  }

  /**
   * 通过密码登录
   * @param form - 登录表单
   */
  const loginByPassword = async (form: LoginByPasswordParams) => {
    const { token } = await ApiLoginByPassword(form)
    return handleLoginSuccess(token)
  }

  /**
   * 通过短信验证码登录
   * @param form - 登录表单
   */
  const loginBySmsCode = async (form: LoginBySmsCodeParams) => {
    const { token } = await ApiLoginBySmsCode(form)
    return handleLoginSuccess(token)
  }

  /**
   * 通过第三方扫码登录
   * @param form - 登录表单
   */
  const loginByScanThird = async (form: LoginByThirdParams) => {
    const { token } = await ApiLoginByThird(form)
    return handleLoginSuccess(token)
  }

  /**
   * 通过第三方绑定后登录 - 第一次扫码但没绑定用户时使用
   * @param form - 登录表单
   */
  const loginByBindThird = async (form: LoginByBindThirdParams) => {
    const { token } = await ApiLoginByBindThird(form)
    return handleLoginSuccess(token)
  }

  /**
   * 通过钉钉授权码登录
   * @param code - 钉钉授权码
   */
  const loginByDingTalk = async (code: string) => {
    const { token } = await loginByDingtalk(code)
    return handleLoginSuccess(token)
  }

  /**
   * 用户登出
   */
  const logout = () => {
    useMyCookies.remove('token')
    useMyStorage.remove('userInfo')
    router.push({ name: 'Login' })
  }

  return { loginByPassword, loginBySmsCode, loginByScanThird, loginByBindThird, loginByDingTalk, logout, token, handleLoginSuccess }
})
