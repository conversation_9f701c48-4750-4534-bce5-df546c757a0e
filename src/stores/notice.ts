export const useNoticeStore = defineStore('notice', () => {
  const notifications = ref<NoticeItem[]>([])
  const totalNotifications = ref(0)

  /**
   * 获取通知类型文本
   * @param type - 通知类型
   */
  function getNoticeTypeText(type: string): string {
    const typeMap: Record<string, string> = {
      1: '个人消息',
      2: '系统公告',
    }
    return typeMap[type] || '系统公告'
  }

  /**
   * 获取通知列表
   * @param params - 分页参数
   */
  async function fetchNotices(params: ListRequestParams) {
    const { list, total } = await ApiGetNotices(params)
    notifications.value = list
    totalNotifications.value = total
    return { list, total }
  }

  /**
   * 标记通知为已读
   * @param noticeId - 通知 ID
   */
  async function markNoticeAsRead(noticeId: number) {
    await ApiGetNoticeDetail(noticeId)
    const notification = notifications.value.find(n => n.noticeId === noticeId)
    if (notification) {
      notification.isRead = '1'
    }
  }

  /**
   * 处理通知点击事件
   * @param item - 被点击的通知项
   */
  async function handleNotificationClick(item: NoticeItem) {
    // 标记为已读，无需 await
    markNoticeAsRead(item.noticeId)
    // 有关联的应用处理跳转
    if (item.appFunction) {
      const appsStore = useAppsStore()
      await appsStore.handleAppOpening(item.appFunction)
    }
  }

  return {
    notifications,
    totalNotifications,
    fetchNotices,
    markNoticeAsRead,
    handleNotificationClick,
    getNoticeTypeText,
  }
})
