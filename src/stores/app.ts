import { ElMessage } from 'element-plus'

export const useAppsStore = defineStore('apps', () => {
  /**
   * 根据颜色名称获取对应的 Tailwind CSS 颜色类
   * @param color 颜色名称
   * @param isHover 是否包含 hover 样式
   * @returns 包含背景和前景色的数组，可直接用于 :class 绑定
   */
  function getTailwindColorByName(color: string = 'blue', isHover: boolean = true): string[] {
    const transition = ['transition-all', 'duration-400']
    const colorMap: Record<string, string[]> = {
      red: ['bg-red-50', 'text-red-700', 'group-hover:bg-red-500'],
      orange: ['bg-orange-50', 'text-orange-700', 'group-hover:bg-orange-500'],
      amber: ['bg-amber-50', 'text-amber-700', 'group-hover:bg-amber-500'],
      yellow: ['bg-yellow-50', 'text-yellow-700', 'group-hover:bg-yellow-500'],
      lime: ['bg-lime-50', 'text-lime-700', 'group-hover:bg-lime-500'],
      green: ['bg-green-50', 'text-green-700', 'group-hover:bg-green-500'],
      emerald: ['bg-emerald-50', 'text-emerald-700', 'group-hover:bg-emerald-500'],
      teal: ['bg-teal-50', 'text-teal-700', 'group-hover:bg-teal-500'],
      cyan: ['bg-cyan-50', 'text-cyan-700', 'group-hover:bg-cyan-500'],
      sky: ['bg-sky-50', 'text-sky-700', 'group-hover:bg-sky-500'],
      blue: ['bg-blue-50', 'text-blue-700', 'group-hover:bg-blue-500'],
      indigo: ['bg-indigo-50', 'text-indigo-700', 'group-hover:bg-indigo-500'],
      violet: ['bg-violet-50', 'text-violet-700', 'group-hover:bg-violet-500'],
      purple: ['bg-purple-50', 'text-purple-700', 'group-hover:bg-purple-500'],
      fuchsia: ['bg-fuchsia-50', 'text-fuchsia-700', 'group-hover:bg-fuchsia-500'],
      pink: ['bg-pink-50', 'text-pink-700', 'group-hover:bg-pink-500'],
      rose: ['bg-rose-50', 'text-rose-700', 'group-hover:bg-rose-500'],
    }
    const defaultColor = ['bg-blue-50', 'text-blue-700', 'group-hover:bg-blue-500']
    const selectedColor = colorMap[color] || defaultColor

    // 根据 isHover 参数决定是否包含 hover 样式
    return isHover ? [...transition, ...selectedColor] : [selectedColor[0], selectedColor[1]]
  }

  const userStore = useUserStore()
  const bindDialogVisible = ref(false)
  const currentApp = ref<AppItem | FunctionItem | null>(null)
  const functionList = ref<FunctionItem[]>([])

  /**
   * 获取功能列表
   */
  async function fetchFunctions(params: ListRequestParams = { page: 1, limit: 100 }): Promise<ListResponse<FunctionItem>> {
    return await ApiGetFunctions(params)
  }

  /**
   * 将绑定类型转换为文字描述
   * @param bindType 绑定类型
   */
  const formatBindType = computed(() => (bindType: AppItem['bindType']): string => {
    const bindTypeMap: Record<AppItem['bindType'], string> = {
      1: '账号密码绑定',
      2: '手机号绑定',
      3: 'OAuth 鉴权跳转',
    }
    return bindTypeMap[bindType] || '未知绑定类型'
  })

  /**
   * 获取应用级别文字描述
   * @param level 应用级别
   */
  const formatAppLevel = computed(() => (level: AppItem['level']): string => {
    const levelMap: Record<AppItem['level'], string> = {
      9: '常用应用',
      4: '国家级应用',
      3: '省级应用',
      2: '市级应用',
      1: '区级应用',
      0: '校级应用',
    }
    return levelMap[level] || '未知级别'
  })

  /**
   * 记录应用点击并跳转到指定 URL
   * @param fullRedirectUrl 完整跳转 URL
   * @param appId 应用 ID
   * @param functionId 功能 ID (可选，如果是应用本身则为空字符串)
   */
  async function recordClickAndRedirect(fullRedirectUrl: string, appId: string, functionId?: string): Promise<void> {
    ApiRecordClick(appId, functionId) // 无需等待返回结果
    ElMessage.success('应用已绑定成功，正在跳转...')
    await new Promise(resolve => setTimeout(resolve, 1000))
    window.open(fullRedirectUrl)
  }

  /**
   * 使用手机号绑定应用
   * @param appId 应用 ID
   * @returns 成功直接跳转到应用
   */
  async function bindAppByPhone(appId: string): Promise<void> {
    if (!userStore.userInfo?.phoneNumber) {
      ElMessage.warning('未获取到您的手机号信息')
      return
    }
    const { fullRedirectUrl } = await ApiBindUserByPhone({
      appId,
      phone: userStore.userInfo.phoneNumber,
    })
    await recordClickAndRedirect(fullRedirectUrl, appId)
  }

  /**
   * 使用账号密码绑定应用
   * @param form 绑定表单数据
   * @returns 成功直接跳转到应用
   */
  async function bindAppByPassword(form: BindUserParams): Promise<void> {
    const { fullRedirectUrl } = await ApiBindUserByPassword(form)
    await recordClickAndRedirect(fullRedirectUrl, form.appId)
  }

  /**
   * 显示账号密码绑定对话框
   * @param app 应用信息
   */
  function showBindDialog(app: AppItem | FunctionItem) {
    currentApp.value = app
    bindDialogVisible.value = true
  }

  /**
   * 获取应用列表
   */
  async function fetchApps(params: ListRequestParams): Promise<ListResponse<AppItem>> {
    return await ApiGetApps(params)
  }

  /**
   * 处理应用授权和绑定
   * 根据应用授权状态，决定是直接打开重定向 URL，还是显示绑定对话框
   * - isBindUser 为 true 时，直接跳转 fullRedirectUrl
   * - isBindUser 为 false 时，根据 bindType 处理
   *  - bindType 为 '1' 时，显示绑定对话框，绑定成功后接口返回 fullRedirectUrl，直接跳转
   *  - bindType 为 '2' 时，直接使用用户手机号进行绑定，绑定成功后接口返回 fullRedirectUrl，直接跳转
   *  - bindType 为 '3' 时，无需检查绑定状态，直接跳转 item.redirectUrl
   * @param item 应用信息 (AppItem 或 FunctionItem)
   */
  async function handleAppOpening(item: AppItem | FunctionItem): Promise<void> {
    const appId = item.appId
    // 如果是 FunctionItem 类型，需要传递 functionId
    const functionId = 'id' in item ? item.id : '' // FunctionItem 有 id 属性
    const itemName = 'id' in item ? item.name : item.appName // FunctionItem 用 name, AppItem 用 appName

    // 第三方应用直接进行 OAuth 鉴权跳转
    if (item.bindType === '3') {
      await recordClickAndRedirect(item.redirectUrl, appId)
      return
    }

    try {
      // 1. 检查绑定状态并获取跳转 URL
      const { isBindUser, fullRedirectUrl, bindType } = await ApiCheckBindAndGetUrl(appId, functionId)

      // 2. 根据状态处理
      if (isBindUser && fullRedirectUrl) {
        // 已绑定，记录点击并直接跳转
        await recordClickAndRedirect(fullRedirectUrl, appId, functionId)
        return
      }

      // 未绑定，进行账号密码绑定，显示对话框
      if (!isBindUser && bindType === '1') {
        showBindDialog(item)
        // 注意：在此情况下，`await handleAppOpening(item)` 会在对话框显示后立即 resolve
        // 后续的绑定操作（如果发生）将在对话框内部异步触发，
        // 包括 `bindAppByPassword` -> `recordClickAndRedirect` -> `fetchApps`
        // 调用 `handleAppOpening` 后立即执行的代码（如 `loadAppsData()`）
        // 可能会在对话框操作完成（并调用 `fetchApps`）之前执行。
        return
      }

      // 未绑定，进行手机号绑定
      if (!isBindUser && bindType === '2') {
        await bindAppByPhone(appId)
        // 确保所有成功路径在操作后返回
      }

      // 确保 Promise<void> 在所有路径都 resolve (隐式 return undefined)
    }
    catch (error: any) {
      console.error(`处理应用 "${itemName}" (AppID: ${appId}, FuncID: ${functionId || 'N/A'}) 打开时出错:`, error)
      // ElMessage.error(`处理应用 "${itemName}" 打开时出错：${error.message || '未知错误'}`)
      throw error // 重新抛出错误，以便调用方可以捕获
    }
  }

  return {
    getTailwindColorByName,
    formatBindType,
    formatAppLevel,
    fetchApps,
    bindDialogVisible,
    currentApp,
    bindAppByPhone,
    bindAppByPassword,
    showBindDialog,
    handleAppOpening,
    functionList,
    fetchFunctions,
  }
})
