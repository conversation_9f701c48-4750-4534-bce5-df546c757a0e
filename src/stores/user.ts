export const useUserStore = defineStore('user', () => {
  const userInfo = ref<UserInfo | null>(null)

  const init = async () => {
    userInfo.value = await useMyStorage.get('userInfo') as UserInfo | null
  }
  init()

  // 获取用户信息
  const fetchUserInfo = async () => {
    const res = await ApiGetUserInfo()
    const profile = {
      ...res,
      avatar: res.avatar || res.auths?.find(auth => auth.avatar)?.avatar || `https://ui-avatars.com/api/?background=0D8ABC&color=fff&name=${res.nickName.slice(-2)}`,
    }
    if (res) {
      userInfo.value = profile
      useMyStorage.set('userInfo', profile)
    }
    return res
  }

  return { userInfo, fetchUserInfo }
})
