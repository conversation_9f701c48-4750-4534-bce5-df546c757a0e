{"name": "app-central-console", "type": "module", "version": "0.1.0", "private": true, "packageManager": "pnpm@10.12.1", "scripts": {"dev:gx": "vite --mode development.gx", "dev:wf": "vite --mode development.wf", "publish:all": "pnpm run publish:staging && pnpm run publish:production", "publish:staging": "git stash && git checkout main && git pull && git reset --hard origin/dev && git push -f && git checkout dev", "publish:production": "git stash && git checkout release && git pull && git reset --hard origin/dev && git push -f && git checkout dev", "build:staging:gx": "vite build --mode staging.gx", "build:staging:wf": "vite build --mode staging.wf", "build:production:gx": "vite build --mode production.gx", "build:production:wf": "vite build --mode production.wf", "postinstall": "npx simple-git-hooks", "typecheck": "vue-tsc --noEmit", "lint": "eslint . --fix"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@headlessui/vue": "^1.7.23", "@heroicons/vue": "^2.2.0", "@vueuse/core": "^13.3.0", "@vueuse/motion": "^3.0.3", "axios": "^1.9.0", "dayjs": "^1.11.13", "dingtalk-jsapi": "^3.1.0", "element-plus": "^2.10.1", "js-cookie": "^3.0.5", "jsencrypt": "^3.3.2", "pinia": "^3.0.3", "unstorage": "^1.16.0", "vue": "^3.5.16", "vue-router": "^4.5.1"}, "devDependencies": {"@antfu/eslint-config": "^4.14.1", "@iconify-json/ep": "^1.2.2", "@iconify-json/heroicons": "^1.2.2", "@iconify-json/mingcute": "^1.2.3", "@iconify-json/ri": "^1.2.5", "@iconify/vue": "^5.0.0", "@types/js-cookie": "^3.0.6", "@types/node": "^24.0.0", "@unocss/eslint-config": "66.1.4", "@unocss/eslint-plugin": "66.1.4", "@unocss/reset": "66.1.4", "@vitejs/plugin-vue": "^5.2.4", "@vue-macros/volar": "3.0.0-beta.14", "autocorrect-node": "^2.14.0", "eslint": "^9.28.0", "eslint-plugin-format": "^1.0.1", "lint-staged": "^16.1.0", "sass": "^1.89.2", "simple-git-hooks": "^2.13.0", "typescript": "^5.8.3", "unocss": "66.1.4", "unocss-preset-scrollbar-hide": "^1.0.1", "unplugin-auto-import": "^19.3.0", "unplugin-vue-components": "^28.7.0", "unplugin-vue-macros": "^2.14.5", "vite": "^6.3.5", "vue-tsc": "^2.2.10"}, "simple-git-hooks": {"pre-commit": "pnpm lint-staged"}, "lint-staged": {"*": ["autocorrect --fix", "eslint --fix"]}, "volta": {"node": "22.16.0"}}