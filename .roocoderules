# Roo Code Rules for Vue 3 + TypeScript + UnoCSS Project

## TypeScript 规范
1. 类型定义：
   - 优先使用 `interface` 定义类型
   - 类型文件统一存放在 `src/types` 目录
   - 禁用 JSDoc 类型注释（除 @param 和 @deprecated 外）

2. 类型检查：
   - 启用严格模式 (strict: true)
   - 必须进行类型声明
   - 使用 `vue-tsc` 进行类型检查

## Vue 组件规范
1. 语法：
   - 必须使用 `<script setup lang="ts">` 语法
   - 必须使用 Composition API
   - 组件文件名使用 PascalCase 命名

2. 组件结构：
   - 模板部分使用单文件组件
   - 样式部分直接使用原子类
   - 禁止使用 `@apply` 等 CSS 预处理指令

## 样式规范
1. 原子类使用：
   - 直接使用 Tailwind/UnoCSS 原子类
   - 类名写在 `class` 属性中
   - 禁止使用 `@apply` 指令

2. 预设：
   - 使用 presetWind3 (Tailwind 兼容模式)
   - 支持属性化模式 (presetAttributify)

## 自动导入配置
1. 自动导入范围：
   - 框架库：vue, pinia, vue-router, @vueuse/core
   - 项目模块：src/composables, src/api, src/types

2. 组件自动导入：
   - 自动导入 src/components 下的组件
   - 自动导入 src/views/**/components 下的组件

## 代码质量规则
1. 代码格式化：
   - 使用 ESLint + Prettier 自动格式化
   - 提交前自动运行 `pnpm lint --fix`

2. 提交规范：
   - 使用 lint-staged 实现提交前检查
   - 禁止提交未修复的 ESLint 错误

3. 其他规则：
   - 禁止 console.log 提交到生产环境
   - 必须处理所有 TypeScript 类型错误

## 图标使用规范
```json
"icons": {
  "format": "<i class=\"{prefix}:{icon-name}\" />",
  "forbidden": ["<el-icon>"],
  "examples": [
    "<i class=\"heroicons:academic-cap\" />",
    "<i class=\"ri:user-line\" />"
  ]
}
```
