import antfu from '@antfu/eslint-config'
import autoImport from './.eslintrc-auto-import.json' with {type: 'json'}

export default antfu(
  {
    formatters: true,
    unocss: true,
    vue: true,
    ignores: ['src/assets/**/*', 'eslint.config.js'],
  },
  {
    languageOptions: {
      globals: autoImport.globals,
    },
  },
  {
    rules: {
      'no-console': 'off',
      'no-undef': 'off',
      'no-unused-vars': 'off',
      'no-restricted-syntax': 'off',
      'n/prefer-global/process': 'off',
      'no-irregular-whitespace': 'off',
      'unused-imports/no-unused-vars': 'off',
      'ts/no-invalid-this': 'off',
      'vue/no-unused-vars': 'warn',
      'vue/component-name-in-template-casing': ['error', 'PascalCase', { registeredComponentsOnly: false, ignores: ['template'] }],
    },
  }
)
