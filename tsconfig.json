{"compilerOptions": {"target": "esnext", "jsx": "preserve", "jsxImportSource": "vue", "lib": ["esnext", "dom"], "useDefineForClassFields": true, "baseUrl": ".", "module": "esnext", "moduleResolution": "bundler", "paths": {"~/*": ["src/*"], "@/*": ["src/*"]}, "resolveJsonModule": true, "types": ["vite/client", "unplugin-vue-macros/macros-global", "element-plus/global"], "allowJs": true, "checkJs": true, "strict": true, "strictNullChecks": true, "noImplicitAny": false, "preserveValueImports": false, "sourceMap": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "isolatedModules": true, "skipDefaultLibCheck": true, "skipLibCheck": true}, "vueCompilerOptions": {"target": 3}, "include": ["*.d.ts", "src/**/*.js", "src/**/*.ts", "src/**/*.tsx", "src/**/*.vue"], "exclude": ["dist", "node_modules"]}