import path from 'node:path'
import Vue from '@vitejs/plugin-vue'

import Unocss from 'unocss/vite'
import AutoImport from 'unplugin-auto-import/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'
import Components from 'unplugin-vue-components/vite'
import VueMacros from 'unplugin-vue-macros/vite'
// import { VueRouterAutoImports } from 'unplugin-vue-router'
// import VueRouter from 'unplugin-vue-router/vite'

import { defineConfig, loadEnv } from 'vite'

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd(), '')
  console.log('🚀 ‣ 当前 API 代理：', env.DEV_SERVER_PROXY_TARGET)

  return {
    base: env.VITE_BASE_URL,

    build: {
      outDir: env.BUILD_SLUG ? `dist_${env.BUILD_SLUG}` : 'dist',
    },

    resolve: {
      alias: {
        '~/': `${path.resolve(__dirname, 'src')}/`,
        '@/': `${path.resolve(__dirname, 'src')}/`,
      },
    },

    css: {
      preprocessorOptions: {
        scss: {
          additionalData: `@use "~/styles/element/index.scss" as *;`,
          api: 'modern-compiler',
        },
      },
    },

    plugins: [
    // https://github.com/vue-macros/vue-macros
      VueMacros({
        defineOptions: false,
        defineModels: false,
        plugins: {
          vue: Vue({
            script: {
              propsDestructure: true,
              defineModel: true,
            },
          }),
        },
      }),

      // https://github.com/antfu/unplugin-auto-import
      AutoImport({
        imports: [
          'vue',
          'pinia',
          'vue-router',
          '@vueuse/core',
        ],
        dts: true,
        dirs: [
          './src/composables',
          './src/api',
          './src/stores',
          './src/types',
        ],
        vueTemplate: true,
        resolvers: [ElementPlusResolver()],
        eslintrc: {
          enabled: true,
        },
      }),

      // https://github.com/antfu/vite-plugin-components
      Components({
      // allow auto load markdown components under `./src/components/`
        extensions: ['vue', 'md'],
        // allow auto import and register components used in markdown
        include: [/\.vue$/, /\.vue\?vue/, /\.md$/],
        resolvers: [
          ElementPlusResolver({
            importStyle: 'sass',
          }),
        ],
        dts: true,
        dirs: ['./src/components', './src/views/**/components'],
      }),

      // https://github.com/antfu/unocss
      // see uno.config.ts for config
      Unocss(),
    ],

    server: {
      open: false,
      host: '0.0.0.0',
      port: Number(env.DEV_SERVER_PORT) || 5173,
      proxy: {
        '/api': {
          target: env.DEV_SERVER_PROXY_TARGET,
          changeOrigin: true,
        },
      },
    },

    esbuild: {
      pure: ['console.log', 'console.warn', 'console.info', 'console.table', 'console.dir'],
      drop: ['debugger'],
      legalComments: 'none',
    },
  }
})
