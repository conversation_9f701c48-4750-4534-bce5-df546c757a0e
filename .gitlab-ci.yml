variables:
  BASE_PATH: /tyyh
  DEPLOY_PATH: ${CI_PROJECT_PATH}
  USER: gxxj
  STAGE_SERVERS: ***********
  PROD_SERVERS: ************ ************
  BRANCHES: wf gx
  DOCKER_IMAGE: 'node:22-alpine'

cache:
  key:
    files:
      - pnpm-lock.yaml
  paths:
    - .pnpm-store/
    - node_modules/

stages:
  - prepare
  - build
  - deploy

.before_script_template: &before_script_template
  before_script:
    - corepack enable
    - pnpm config set store-dir .pnpm-store
    - echo "Node $(node -v)"
    - echo "Pnpm $(pnpm -v)"

prepare:dependencies:staging:
  stage: prepare
  image: ${DOCKER_IMAGE}
  tags:
    - dev-docker_node:lts-alpine
  <<: *before_script_template
  only:
    - main
    - wf
    - gx
  script:
    - export SKIP_INSTALL_SIMPLE_GIT_HOOKS=1
    - pnpm i --frozen-lockfile
  artifacts:
    expire_in: 1 hour
    paths:
      - node_modules/

prepare:dependencies:production:
  stage: prepare
  image: ${DOCKER_IMAGE}
  tags:
    - edu-docker_node:lts-alpine
  <<: *before_script_template
  only:
    - release
    - wf
    - gx
  script:
    - export SKIP_INSTALL_SIMPLE_GIT_HOOKS=1
    - pnpm i --frozen-lockfile
  artifacts:
    expire_in: 1 hour
    paths:
      - node_modules/

.build_staging_template: &build_staging_template
  stage: build
  image: ${DOCKER_IMAGE}
  tags:
    - dev-docker_node:lts-alpine
  <<: *before_script_template
  needs:
    - prepare:dependencies:staging
  artifacts:
    expire_in: 1 day

.build_production_template: &build_production_template
  stage: build
  image: ${DOCKER_IMAGE}
  tags:
    - edu-docker_node:lts-alpine
  <<: *before_script_template
  needs:
    - prepare:dependencies:production
  artifacts:
    expire_in: 1 day

build:staging:wf:
  <<: *build_staging_template
  only:
    - main
    - wf
  script:
    - pnpm run build:staging:wf
  artifacts:
    paths:
      - dist_wf

build:staging:gx:
  <<: *build_staging_template
  only:
    - main
    - gx
  script:
    - pnpm run build:staging:gx
  artifacts:
    paths:
      - dist_gx

build:production:wf:
  <<: *build_production_template
  only:
    - release
    - wf
  script:
    - pnpm run build:production:wf
  artifacts:
    paths:
      - dist_wf

build:production:gx:
  <<: *build_production_template
  only:
    - release
    - gx
  script:
    - pnpm run build:production:gx
  artifacts:
    paths:
      - dist_gx

deploy:staging:
  stage: deploy
  tags:
    - dev-frontend-03
  only:
    - main
    - wf
    - gx
  needs:
    - job: build:staging:wf
      optional: true
    - job: build:staging:gx
      optional: true
  script:
    - |
      if [ "$CI_COMMIT_REF_NAME" == "main" ]; then
        for branch in $BRANCHES; do
          if [ -d "dist_${branch}" ]; then
            for server in $STAGE_SERVERS; do
              echo "---> Server: $server"
              echo "---> Branch: ${branch}"
              ssh "${USER}@${server}" "mkdir -p ${DEPLOY_PATH}/${branch}${BASE_PATH} && cd ${DEPLOY_PATH}/${branch}${BASE_PATH} && pwd && exit"
              rsync -avzq --delete "dist_${branch}/" "${USER}@${server}:${DEPLOY_PATH}/${branch}${BASE_PATH}"
            done
          else
            echo "No build artifacts found for ${branch}"
          fi
        done
      else
        if [ -d "dist_${CI_COMMIT_REF_NAME}" ]; then
          for server in $STAGE_SERVERS; do
            echo "---> Server: $server"
            echo "---> Branch: ${CI_COMMIT_REF_NAME}"
            ssh "${USER}@${server}" "mkdir -p ${DEPLOY_PATH}/${CI_COMMIT_REF_NAME}${BASE_PATH} && cd ${DEPLOY_PATH}/${CI_COMMIT_REF_NAME}${BASE_PATH} && pwd && exit"
            rsync -avzq --delete "dist_${CI_COMMIT_REF_NAME}/" "${USER}@${server}:${DEPLOY_PATH}/${CI_COMMIT_REF_NAME}${BASE_PATH}"
          done
        else
          echo "No build artifacts found for ${CI_COMMIT_REF_NAME}"
        fi
      fi

deploy:production:
  stage: deploy
  tags:
    - edu-frontend-02
  only:
    - release
    - wf
    - gx
  needs:
    - job: build:production:wf
      optional: true
    - job: build:production:gx
      optional: true
  script:
    - |
      if [ "$CI_COMMIT_REF_NAME" == "release" ]; then
        for branch in $BRANCHES; do
          if [ -d "dist_${branch}" ]; then
            for server in $PROD_SERVERS; do
              echo "---> Server: $server"
              echo "---> Branch: ${branch}"
              ssh "${USER}@${server}" "mkdir -p ${DEPLOY_PATH}/${branch}${BASE_PATH} && cd ${DEPLOY_PATH}/${branch}${BASE_PATH} && pwd && exit"
              rsync -avzq --delete "dist_${branch}/" "${USER}@${server}:${DEPLOY_PATH}/${branch}${BASE_PATH}"
            done
          else
            echo "No build artifacts found for ${branch}"
          fi
        done
      else
        if [ -d "dist_${CI_COMMIT_REF_NAME}" ]; then
          for server in $PROD_SERVERS; do
            echo "---> Server: $server"
            echo "---> Branch: ${CI_COMMIT_REF_NAME}"
            ssh "${USER}@${server}" "mkdir -p ${DEPLOY_PATH}/${CI_COMMIT_REF_NAME}${BASE_PATH} && cd ${DEPLOY_PATH}/${CI_COMMIT_REF_NAME}${BASE_PATH} && pwd && exit"
            rsync -avzq --delete "dist_${CI_COMMIT_REF_NAME}/" "${USER}@${server}:${DEPLOY_PATH}/${CI_COMMIT_REF_NAME}${BASE_PATH}"
          done
        else
          echo "No build artifacts found for ${CI_COMMIT_REF_NAME}"
        fi
      fi
